@font-face {
    font-family: 'Honeywell Sans Web';
    src: url('semantic/themes/default/assets/fonts/HoneywellCondWeb-Book.eot');
    src: url('semantic/themes/default/assets/fonts/HoneywellCondWeb-Book.eot?#iefix') format('embedded-opentype'),
         url('semantic/themes/default/assets/fonts/HoneywellSans-Medium.otf') format('opentype'),
         url('semantic/themes/default/assets/fonts/HoneywellSans-Light.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
/* Global focus styling for all elements except buttons */
:focus:not(.ui.button) {
    outline: 1px solid #90adee64 !important;
}

/* Remove all default styling from buttons - they should only show styling when necessary */
.ui.button {
    outline: none !important;
    box-shadow: none !important;
}

/* Only show focus outline for buttons when they are actively focused (not by default) */
.ui.button:focus {
    outline: 1px solid #90adee64 !important;
}

/* Remove default highlight from save buttons - only apply when intentionally highlighted */
.ui.button.tab-nav-save-highlight {
    /* Only show highlight when class is explicitly applied via JavaScript during navigation */
    outline: 3px solid #90adee !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(144, 173, 238, 0.3) !important;
}

/* Enhanced tab navigation visual feedback - finite animations for better performance */
.tab-nav-save-highlight {
    animation: saveButtonPulse 0.8s ease-in-out 2 alternate !important;
}

.tab-nav-element-highlight {
    animation: elementHighlight 0.5s ease-in-out !important;
}

@keyframes saveButtonPulse {
    0% {
        box-shadow: 0 0 0 2px rgba(144, 173, 238, 0.4), 0 0 8px rgba(144, 173, 238, 0.2) !important;
        outline: 2px solid #90adee !important;
        outline-offset: 1px !important;
    }
    100% {
        box-shadow: 0 0 0 3px rgba(144, 173, 238, 0.6), 0 0 12px rgba(144, 173, 238, 0.4) !important;
        outline: 2px solid #90adee !important;
        outline-offset: 2px !important;
    }
}

@keyframes elementHighlight {
    0% {
        box-shadow: 0 0 0 2px #90adee, 0 0 10px rgba(144, 173, 238, 0.3) !important;
    }
    50% {
        box-shadow: 0 0 0 4px #90adee, 0 0 20px rgba(144, 173, 238, 0.6) !important;
    }
    100% {
        box-shadow: 0 0 0 2px #90adee, 0 0 10px rgba(144, 173, 238, 0.3) !important;
    }
}

/* Enhanced save button visibility - only when focused or actively highlighted during navigation */
.honeywell-device-wizard .button-group .ui.button:focus {
    outline: 2px solid #90adee !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(144, 173, 238, 0.3) !important;
}

/* Save button highlight during tab navigation (temporary visual aid) */
.honeywell-device-wizard .button-group .ui.button.tab-nav-save-highlight {
    outline: 3px solid #90adee !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(144, 173, 238, 0.3) !important;
}

/* Enhanced general button focus visibility - only when focused */
.honeywell-device-wizard .ui.button:focus {
    outline: 2px solid #90adee !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 3px rgba(144, 173, 238, 0.3) !important;
}
.honeywell-device-wizard #honeywell-logo {
    height: 20px;
    width: 100px;
}
.honeywell-device-wizard .verticalLine {
    border-left: 1px solid #ffffff;
    margin-top:18px;
    margin-bottom:18px;
}
.honeywell-device-wizard #thermoTitle{
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
    font-size:16px;
    color: #E0E0E0;
}
.honeywell-device-wizard .menuItemWidth {
    min-width: 0em !important;
    height: 55px !important;
}
.honeywell-device-wizard #Mask-dashboard {
    transform: translate(0px, -4px) !important;
}
.honeywell-device-wizard .menuActive #Mask-dashboard{
    fill: #FFF !important;
}
.honeywell-device-wizard .menuActive{
    color: white !important;
    background-color :"#252525";
    height:4em !important;
}
.honeywell-device-wizard .menuActive #Tint-Icon {
    fill: #FFF !important;
}
.honeywell-device-wizard .nav-icon-block {
    top: 44%;
    position: absolute;
    transform: translateY(-50%);
    fill: #FFF !important;
}
.honeywell-device-wizard nav-icon-block1{
    fill: #EE3124 !important;
}

/* General Comp CSS */
.honeywell-device-wizard .ui.form .field>.selection.dropdown {
    width: 18% ! important;
}
.honeywell-device-wizard #headingLabel{
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
    font-size:16px;
    color: black;
    font-weight: 800;
    letter-spacing: 0.25px;
}
.honeywell-device-wizard .headingLabel{
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
    font-size:16px;
    color: black;
    font-weight: 700;
}
.honeywell-device-wizard #subheadingLabel{
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
    font-size:12px;
    color: black;
    font-weight: 800;
}
.honeywell-device-wizard #subheadingLabel2{
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
    font-size:12px;
    color: black;
    font-weight: 800;
}
.honeywell-device-wizard .bold{
    color: black;
    font-weight: 800;
}
.honeywell-device-wizard .ui.form .inline.field>.selection.dropdown, .ui.form .inline.fields .field>.selection.dropdown{
    margin-left:89px ! important
}
.honeywell-device-wizard .ui.form input:not([type]), .ui.form input[type=date], .ui.form input[type=datetime-local], .ui.form input[type=email], .ui.form input[type=file], .ui.form input[type=number], .ui.form input[type=password], .ui.form input[type=search], .ui.form input[type=tel], .ui.form input[type=text], .ui.form input[type=time], .ui.form input[type=url]{
    border-radius:0px ! important
}
.honeywell-device-wizard .ui.form .inline.field:not(.wide) .ui.input, .ui.form .inline.fields .field:not(.wide) .ui.input{
    width:111px ! important;
    margin-left:1px ! important;
}
.honeywell-device-wizard #powerInput{
    margin-left:42px !important
}
.honeywell-device-wizard .ui.secondary.pointing.menu .active.item{
    border-color:steelblue ! important;
    border-bottom-width:4px ! important;
}
.honeywell-device-wizard .ui.radio.checkbox input:checked~.box:after, .ui.radio.checkbox input:checked~label:after{
    background-color:steelblue ! important;
}
.honeywell-device-wizard .ui.form .inline.fields>label{
    width:155px !important;
    font-family: 'Honeywell Sans Web', Arial, sans-serif !important;
    font-size: 14px;
}
.honeywell-device-wizard .ui.form .inline.field>:first-child, .ui.form .inline.fields .field>:first-child{
    font-family: 'Honeywell Sans Web', Arial, sans-serif !important;
    font-size: 14px;
}
.honeywell-device-wizard .ui.form{
    margin-top:15px ! important
}
.honeywell-device-wizard .ui.secondary.pointing.menu{
    font-family: 'Honeywell Sans Web', Arial, sans-serif !important;
    font-size:14px ! important;
    color: black ! important;
    font-weight: 800 ! important;
}

.honeywell-device-wizard .hon-dynamic-page-tab .ui.secondary.pointing.menu{
    border-bottom: 0px;
}

/* new component css*/
.honeywell-device-wizard .ui.form select{
    border-radius:0px !important
}
.honeywell-device-wizard .ui.input>input{
    margin-left:42px !important
}
.honeywell-device-wizard .ui.button{
    background:steelblue;
    color:white;
    opacity: unset !important;
    font-family: 'Honeywell Sans Web', Arial, sans-serif !important;
}
.honeywell-device-wizard .ui.modal{
    width:600px !important;
}
.honeywell-device-wizard .ui.button:disabled{
    opacity: .45!important;
}
@media only screen and (max-height: 600px) {
    .common-div {
        overflow-y:scroll;
        max-height:250px;
    }
    .button-group{
        position: relative;
        bottom: -8px;
        right: 19.8%;
        float: right;
        margin-top: 10px;
    }
}
@media only screen and (min-height: 600px) {
    .common-div {
        overflow-y:scroll;
        max-height:400px;
    }
    .button-group{
        position: relative;
        bottom: 48px;
        right: 19.7%;
        float: right;
        margin-top: 10px;
    }
}
@media only screen and (min-height: 768px) {
    .common-div {
        overflow-y:scroll;
        max-height:560px;
    }
    .button-group{
        position: relative;
        bottom: 48px;
        right: 19.5%;
        float: right;
        margin-top: 10px;
    }
}
@media only screen and (min-height: 900px) {
    .common-div {
        overflow-y:scroll;
        max-height:700px;
    }
    .button-group{
        position: fixed;
        bottom: 8px;
        right: 19%;
        float:right;
        margin-top:10px
    }
}
@media only screen and (min-height: 1200px) {
    .common-div {
        overflow-y:scroll;
        max-height:1000px;
    }
    .button-group{
        position: fixed;
        bottom: 8px;
        right: 18.9%;
        float:right;
        margin-top:10px
    }
}
@media only screen and (min-height: 1400px) {
    .common-div {
        overflow-y:scroll;
        max-height:1100px;
    }
    .button-group{
        position: fixed;
        bottom: 8px;
        right: 18.9%;
        float:right;
        margin-top:10px
    }
}
@media only screen and (min-height: 1600px) {
    .common-div {
        overflow-y:scroll;
        max-height:1200px;
    }
    .button-group{
        position: fixed;
        bottom: 8px;
        right: 18.8%;
        float:right;
        margin-top:10px
    }
}
.honeywell-device-wizard .ui.segment{
    border:none;
}
.honeywell-device-wizard .ui.attached.segment{
    border:none;
    width: calc(100% + 0px) !important;
    max-width: calc(100% + 0px) !important;
}
.honeywell-device-wizard .message{
    width: 500px;
    height: 100px;
    position: absolute;
    top:0;
    bottom: 0;
    left: 50;
    right: 0;
    margin: auto;
}
.honeywell-device-wizard .ui.grid>[class*="two column"].row>.column {
    width: 35%!important;
}
.honeywell-device-wizard .ui.grid>[class*="three column"].row>.column{
    width:15.333333%!important
}
.honeywell-device-wizard .ui.grid>[class*="four column"].row>.column{
    width:20%!important
}
.honeywell-device-wizard .switch {
    position: relative;
    display: inline-block;
    width: 92px;
    height: 31px;
}
.honeywell-device-wizard .switch input {display:none;}
.honeywell-device-wizard .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: steelblue;
    -webkit-transition: .4s;
    transition: .4s;
}
.honeywell-device-wizard .slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 21px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}
.honeywell-device-wizard input:checked + .slider {
    background-color:steelblue;
}
.honeywell-device-wizard input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}
.honeywell-device-wizard input:checked + .slider:before {
    -webkit-transform: translateX(55px);
    -ms-transform: translateX(55px);
    transform: translateX(55px);
}

/*------ ADDED CSS ---------*/
.honeywell-device-wizard .on {
    display: none;
}
.honeywell-device-wizard .on, .off {
    color: white;
    position: absolute;
    transform: translate(-50%,-50%);
    top: 50%;
    left: 53%;
    font-size: 10px;
    font-family: Verdana, sans-serif;
}
.honeywell-device-wizard input:checked+ .slider .on
{display: block;}
.honeywell-device-wizard input:checked + .slider .off
{display: none;}

/*--------- END --------*/
/* Rounded sliders */
.honeywell-device-wizard .slider.round {
    border-radius: 34px;
}
.honeywell-device-wizard .slider.round:before {
    border-radius: 40%;
}
.honeywell-device-wizard .ui.container{
    margin-left:5px !important;
}
.honeywell-device-wizard .ui.segment{
    background:#F4F4F4 !important;
    padding: 10px 0 0 4px;
    margin: 0px !important;
    box-shadow: none;
    -webkit-box-shadow: none;
}
.honeywell-device-wizard .ui.dropdown .menu{
    border:0px !important;
    margin-top: 5px !important;
    border-radius: 4px !important;
}
.honeywell-device-wizard .newInput{
    width:176px ! important;
}
.honeywell-device-wizard .ui.grid{
    margin-top:-1.6rem ! important;
    flex-direction: row-reverse;
}

/*
.ux-root input[type=color][readonly], .ux-root input[type=date][readonly], .ux-root input[type=datetime][readonly], .ux-root input[type=datetime-local][readonly], .ux-root input[type=email][readonly], .ux-root input[type=month][readonly], .ux-root input[type=number][readonly], .ux-root input[type=password][readonly], .ux-root input[type=search][readonly], .ux-root input[type=text][readonly], .ux-root input[type=time][readonly], .ux-root input[type=url][readonly], .ux-root textarea[readonly], .ux-root input[type=color][disabled], .ux-root input[type=date][disabled], .ux-root input[type=datetime][disabled], .ux-root input[type=datetime-local][disabled], .ux-root input[type=email][disabled], .ux-root input[type=month][disabled], .ux-root input[type=number][disabled], .ux-root input[type=password][disabled], .ux-root input[type=search][disabled], .ux-root input[type=text][disabled], .ux-root input[type=time][disabled], .ux-root input[type=url][disabled], .ux-root textarea[disabled]{
background-color:white ! important;
}*/
@media only screen and (max-height: 600px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:600px;
        overflow-x: hidden;
        width: 99%;
        height: 20rem;
    }
}
@media only screen and (min-height: 600px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:420px;
        overflow-x: hidden;
    }
}
@media only screen and (min-height: 768px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:768px;
        overflow-x: hidden;
        width: 99%;
        height: 40rem;
    }
}
@media only screen and (min-height: 900px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:700px;
        overflow-x: hidden;
        width: 99%;
        height: 40rem;
    }
}
@media only screen and (min-height: 1200px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:1000px;
        overflow-x: hidden;
    }
}
@media only screen and (min-height: 1400px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:1000px;
        overflow-x: "hidden";
    }
}
@media only screen and (min-height: 1600px) {
    .common-div1 {
        overflow-y:scroll;
        max-height:1200px;
        overflow-x: hidden;
    }
}
.honeywell-device-wizard .ui.top.left.popup{
    margin-left:20px !important;
}
.honeywell-device-wizard .vertical-align-icon{
    vertical-align: middle !important;
}
.honeywell-device-wizard .ui.grid>.row>.column {
    margin-top: 10px;
    padding-left: 1.5rem;
}
.honeywell-device-wizard .ux-root {
    color: black;
}

.configure-holiday-modal, .add-event-modal {
    padding-right: 0px !important;
}

.configure-holiday-modal .tc-component, .add-event-modal .tc-component{
    margin-right: 25px !important;
}

.honeywell-device-wizard .tc-component {
    margin-bottom: 25px;
}

.honeywell-device-wizard .tc-component>.tc-component {
    margin-bottom: 0px;
}

.honeywell-device-wizard .tc-component .hon-radio-button {
    margin-right: 20px;
}

.honeywell-device-wizard .tc-component .ui.input>input {
    margin-left: 0px !important;
    border: 1px solid #707070 !important;
    width: 205.6px;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.honeywell-device-wizard .tc-component-group-inline {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-end;
    flex-wrap: wrap;
}

.honeywell-device-wizard .tc-component-group-inline .tc-component {
    margin-right: 4em;
    margin-top: 0px;
}

.honeywell-device-wizard .tc-page-container {
    padding-left: 1em;
    overflow-y:scroll;
    height: calc(100vh - 150px) !important;
}

.honeywell-device-wizard .hon-dynamic-page-tab {
    height: 100%;
}

.honeywell-device-wizard .hon-dynamic-page-tab .tc-page-container {
    padding-left: 0px;
    overflow-y:scroll;
    height: 100% !important;
    margin-left: -12px;
    margin-right: 4px;
    padding-right: 10px;
}

.honeywell-device-wizard .mainContainer {
    min-height: 400px;
}

.honeywell-device-wizard .ui.menu {
    font-family: "Source Sans Pro", SourceSansPro, sans-serif !important;
}

.honeywell-device-wizard .hon-select-button .ui.menu .active.item:hover {
    background-color: #2185d0;
    color: #fff;
}

.honeywell-device-wizard .hon-select-button .ui.menu .active.item {
    background: #2185d0;
    color: #fff;
    border-radius: 5px;
}

.honeywell-device-wizard .service-mode-main {
    overflow-y:scroll;
    height: calc(100vh - 80px) !important;
}

.honeywell-device-wizard .alarm-view-main {
    height: calc(100vh - 80px) !important;
}

.honeywell-device-wizard .alarm-view {
    overflow-y:scroll;
    height:calc(100% - 40px);
    overflow-x: hidden;
}

.honeywell-device-wizard .ui.segment[class*="bottom attached"]:last-child{
    height: calc(100vh - 130px) !important;
}

.honeywell-device-wizard .hon-dynamic-page-tab .ui.segment[class*="bottom attached"]:last-child{
    height: calc(100% - 56px) !important;
}

.honeywell-device-wizard .rightMain .ui.grid .three.wide.column {
    padding-left: 0px !important;
    padding-bottom: 0px !important;
}

.honeywell-device-wizard .rightMain .ui.grid .three.wide.column .ui.vertical.menu {
    width: 100%;
    height: calc(100vh - 55px) !important;
    border: none;
    box-shadow: none;
    overflow-y: auto !important;
}

.honeywell-device-wizard .rightMain .ui.grid .three.wide.column .ui.fluid.vertical.tabular.menu {
    height: 100%;
}

.honeywell-device-wizard .rightMain .ui.grid .three.wide.column .ui.fluid.vertical.tabular.menu a.active.item {
    margin-right: -2px;
}

.honeywell-device-wizard .rightMain .ui.grid .thirteen.wide.column .ui.bottom.attached.segment.active.tab {
    background: #F4F4F4 !important;
    padding: 20px 0 0 20px;
}

.honeywell-device-wizard .rightMain .ui.grid .thirteen.wide.column .hon-dynamic-page-tab .ui.bottom.attached.segment.active.tab {
    border-top: none;
    border-bottom: none;
    border-left: none;
    border-right: none;
    padding-bottom: 0px;
    padding-right: 0px;
}

.honeywell-device-wizard .tc-component .ui.input.mini-hon {
    font-size: 0.75em;
}

.honeywell-device-wizard .tc-component .ui.input.mini-hon>input {
    padding: 0.5em 0.5em;
}

.honeywell-device-wizard .vertical-tab-menu {
    display: flex !important;
    justify-content: space-between;
}

.honeywell-device-wizard .vertical-tab-menu .tab-menu-icon {
    width: 14px;
    margin: 3px 0 -3px 0;
}

.icon {
    cursor: pointer;
}

.dropdown-mini-hon.ui.selection.dropdown {
    width: 127.5px;
    height: 25.25px;
    padding-top: 0.3em;
    padding-bottom: 0.5em;
    padding-left: 0.5em;
    padding-right: 0.5em;
    min-width: 0px;
    min-height: 0px;
}

.dropdown-mini-hon.ui.selection.dropdown .divider.text {
    font-size: 0.9em;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: block;
    margin-right: 15px;
}

.dropdown-mini-hon.ui.selection.dropdown .dropdown.icon {
    padding: 0.5em;
}

.dropdown-mini-hon.ui.selection.dropdown .menu.transition>.item {
    font-size: 0.85em;
    padding: 0.5em !important;
    height: 38px;
    align-content: center;
}

.switchbutton-mini-hon.ui.toggle.checkbox {
    height: 17px;
    min-height: 17px;
}

.switchbutton-mini-hon.ui.toggle.checkbox label:before {
    width: 34px;
    height: 17px;
}

.switchbutton-mini-hon.ui.toggle.checkbox label:after {
    width: 17px;
    height: 17px;
}

.switchbutton-mini-hon.ui.toggle.checkbox input:checked~label:after {
    left: 17px;
}

.hon-radio-button.ui.checkbox label {
    font-size: 0.87em;
}

.hon-select-button .ui.item.menu {
    min-height: 0px;
    height: 26px;
}

.hon-select-button .ui.item.menu .item {
    font-size: 0.87em;
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

.hon-select-widget {
    display: inline-flex;
    background: white;
    border-radius: 6px;
    padding: 2px;
}

.hon-select-widget .select-widget-button.ui.button {
    padding-bottom: 6px;
    height: 7em;
    background-color: #ffffff00;
    color: #303030;    
    border-radius: 6px;
    min-width: 110px;
    font-family: 'Honeywell Sans Web', Arial, sans-serif !important;
    font-weight: 700;
    font-size: 11px;
    line-height: 14.33px;
    letter-spacing: 1.49px;
    text-transform: uppercase;
    /* Smooth transitions for focus changes */
    transition: outline 0.1s ease, box-shadow 0.1s ease;
}

.hon-select-widget .select-widget-button.ui.button.no-icon {
    height: 40px;
}

.hon-select-widget .select-widget-button.selected.ui.button {
    background-color: #90adee64;
    border: 3px solid #90adee64;
    margin-right: 0px;
    padding-bottom: 6px;
}

/* Enhanced focus styles for SelectWidget buttons - using inset shadow to avoid clipping */
.hon-select-widget .select-widget-button.ui.button:focus {
    box-shadow: inset 0 0 0 2px #90adee, 0 0 0 1px rgba(144, 173, 238, 0.3) !important;
    outline: none !important;
}

/* Focused and selected state combination - using inset shadow to avoid clipping */
.hon-select-widget .select-widget-button.selected.ui.button:focus {
    box-shadow: inset 0 0 0 2px #90adee, 0 0 0 1px rgba(144, 173, 238, 0.3) !important;
    outline: none !important;
}

.hon-select-widget .select-widget-button .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 100%;
}

.honeywell-device-wizard .hon-slider-container {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    padding-right: 1em;
}

.honeywell-device-wizard .ui.vertical.menu .item:before {
    width: 0% !important;
}

.honeywell-device-wizard .ui.vertical.menu .item {
    font-weight: 700;
    letter-spacing: 2.5px;
    padding-left: 8px;
    font-size: 15px;
    padding: 15px 0 0 10px;
}

.honeywell-device-wizard .ui.vertical.menu .active.item{
    color: #5F8FF1 !important;
    font-weight: 800;
    font-size: 15px;
    letter-spacing: 2.5px;
    background: transparent !important;
}

.honeywell-device-wizard .stretched.thirteen.wide.column {
    padding-right: 0 !important;
}

.honeywell-device-wizard .vertical-tab-menu-item {
    margin: 3px 0 11px -14px;
    padding-left: 34px !important;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.honeywell-device-wizard .ui.vertical.menu .item:hover {
    background: none !important;
}

.WebShell ::-webkit-scrollbar, .js-dialog-container ::-webkit-scrollbar {
    width: 4px;
}

body ::-webkit-scrollbar {
    width: 4px !important;
}

.honeywell-device-wizard .left-arrow {
    width: 0;
    height: 0;
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 15px solid #F4F4F4;
    background: white;
    position: absolute;
    left: 0;
    top: 8px;
}


.honeywell-device-wizard .input-container {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.honeywell-device-wizard .input-container:focus-within .input-label {
  transform: translate(0, 12px) scale(0.8);
  color: #0a53e4;
}

.honeywell-device-wizard .input-container .filled {
  transform: translate(0, 12px) scale(0.8) !important;
}

.honeywell-device-wizard .input-container .input-label {
  position: absolute;
  pointer-events: none;
  transform: translate(0, 23px) scale(1);
  transform-origin: top left;
  transition: 200ms cubic-bezier(0, 0, 0.2, 1) 0ms;
  color: #49454F;
  line-height: 1;
  left: 12px;
  bottom: 30px;
  z-index: 1;
}

.honeywell-device-wizard .input-container input {
  height: 45px;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1;
  outline: none;
  box-shadow: none;
  transition: 200ms cubic-bezier(0, 0, 0.2, 1) 0ms;
  padding: 18px 0 0 12px !important;
  font-weight: 700;
}

.honeywell-device-wizard .text-input-container input {
    font-family: "Source Sans Pro", SourceSansPro, sans-serif;
    font-size: 12.5px;
    border: 1px solid #707070 !important;
}

.honeywell-device-wizard .dropdown-container {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.honeywell-device-wizard .dropdown-container .dropdown-mini-hon.ui.selection.dropdown {
    height: 45px;
    border-radius: 4px;
    font-size: 16px;
    line-height: 1;
    outline: none;
    box-shadow: none;
    transition: 200ms cubic-bezier(0, 0, 0.2, 1) 0ms;
    padding: 20px 0 0 12px !important;
    font-weight: 700;
    width: 205.6px;
    border: 1px solid #707070 !important;
}

.ui.selection.visible.dropdown>.text:not(.default) {
    font-weight: 700 !important;
}

.honeywell-device-wizard .dropdown-container .timezone-comp.ui.selection.dropdown {
    width: 329px !important;
}

.honeywell-device-wizard .dropdown-container .dropdown-label {
    position: absolute;
    pointer-events: none;
    transform: translate(0, 23px) scale(1);
    transform-origin: top left;
    transition: 200ms cubic-bezier(0, 0, 0.2, 1) 0ms;
    color: #49454F;
    line-height: 1;
    left: 12px;
    bottom: 30px;
    z-index: 1;
}

.honeywell-device-wizard .dropdown-container:focus-within .dropdown-label {
    transform: translate(0, 12px) scale(0.8);
    color: #0a53e4;
    z-index: 11;
}

.honeywell-device-wizard .dropdown-container .filled {
    transform: translate(0, 12px) scale(0.8) !important;
    z-index: 1;
}

.honeywell-device-wizard .dropdown-container i.dropdown.icon {
    padding: 20px 6px 0 12px !important;
}

.timeline-container {
    display: flex;
    justify-content: space-around;
    margin: 8px 10px 4px 0px;
    color: #3C3C3C;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.timeline-bar-container {
    height: 40px;
    display: flex;
    margin-bottom: 5px;
    position: relative;
}

.timeline-bar {
    border: 1px solid #DCDCDC;
    width: 89%;
    height: 40px;
    display: inline-flex;
    position: absolute;
    left: 5%;
    background: #FFFFFF;
    border-radius: 4px;
}

.timeline-bar-day {
    position: absolute;
    top: 8px;
    left: 16px;
    color: #3C3C3C;
    font-family: 'Honeywell Sans Web', Arial, sans-serif;
}

.timeline-hours {
    width: 1.036%;
}

.add-event {
    position: absolute;
    cursor: pointer;
    right: -3%;
    top: 10px;
    text-align: center;
    color: #1792E5;
    display: flex;
}

.event-info-container {
    display: flex;
    flex-direction: row;
    justify-content: end;
    margin: 1% 4% 0 0;
}

.event-info {
    margin: 0 11px 0 11px;
    font-size: 11px;
}

.event-info-status {
    text-align: center;
    margin-left: -4px;
}

.no-event {
    height: 10px;
    width: 52px;
    border: 1px solid #F4F4F4;
    background: #FFFFFF;
    border-radius: 4px;
    margin-left: -4px;
}

.no-event-info {
    line-height: normal;
    font-size: 10px;
    position: absolute;
    margin-top: 4px;
}

.ghost-event-scheduleType1 {
  background-color: #89CC25;
  border: 1px dashed #a0c4ff;
  border-radius: 4px;
  z-index: 5;
  pointer-events: none;
}

.ghost-event-scheduleType2 {
  background-color: #848483;
  border: 1px dashed #a0c4ff;
  border-radius: 4px;
  z-index: 5;
  pointer-events: none;
}

.ghost-event-scheduleType3 {
  background-color: #EDE900;
  border: 1px dashed #a0c4ff;
  border-radius: 4px;
  z-index: 5;
  pointer-events: none;
}

.scheduleType1 {
    height: 10px;
    width: 52px;
    border: 1px solid #F4F4F4;
    background: #89CC25;
    border-radius: 4px;
}

.scheduleType1-color {
    background: #89CC25;
    cursor: pointer;
}

.scheduleType2 {
    height: 10px;
    width: 52px;
    border: 1px solid #F4F4F4;
    background: #848483;
    border-radius: 4px;
}

.scheduleType2-color {
    background: #848483;
    cursor: pointer;
}

.scheduleType3 {
    height: 10px;
    width: 52px;
    border: 1px solid #F4F4F4;
    background: #EDE900;
    border-radius: 4px;
}

.scheduleType3-color {
    background: #EDE900;
    cursor: pointer;
}

.add-event-startat-endat-container {
    display: flex;
}

.warning-info {
    color: red;
}

.hide {
    display: none !important;
}

.copy-event-container {
    display: flex;
    justify-content: space-around;
    margin-left: -8%;
}

.copy-event-days-container {
    border: 1px solid #707070;
    border-radius: 4px;
    width: 220px;
    padding: 10px;
}

.select-all-button-container {
    margin-bottom: 8px;
}

.select-all-button {
    width: 148px;
    color: #505050 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    background: none !important;
    border: 1px solid #505050 !important;
}

.event-day {
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.5px;
    cursor: pointer;
}

.event-day-selected-blue {
    color: #90ADEE;
}

.event-day-selected-green {
    color: #459016;
    cursor: default !important;
}

.target-weekday {
    font-size: 11px;
    font-weight: 500;
    line-height: 16px;
    color: #49454F;
}

.add-event-button {
    background-color: #606060 !important;
    color: white !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    right: 3.5%;
    position: relative;
    margin-right: 10px !important;
}

.add-event-cancel-button {
    color: #505050 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    background: none !important;
    border: 1px solid #505050 !important;
    left: 2%;
    position: absolute;
    margin-left: 10px !important;
}

.actions {
    background: none !important;
    border: none !important;
    padding-top: 0px !important;
    padding-right: 0px !important;
    background-color: #F4F4F4 !important;
}

.ui.modal>.content {
    padding-bottom: 0px;
    padding-top: 0px;
    background-color: #F4F4F4 !important;
}

.header {
    border: none !important;
    padding-bottom: 0px !important;
    background-color: #F4F4F4 !important;
}

.error-modal-close-button {
    margin-right: 4% !important;
    right: 40%;
    position: relative;
}

.error-info {
    overflow-wrap: anywhere;
    overflow-y: auto;
    max-height: 400px;
}

.button-group>button {
    background-color: #606060 !important;
    color: white !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    right: 9%;
    position: relative;
}

.copyto-button {
    background-color: #606060 !important;
    color: white !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    right: 9%;
    position: relative;
}

.copyto-cancel-button {
    color: #505050 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    background: none !important;
    border: 1px solid #505050 !important;
    left: 2%;
    position: absolute;
}

.honeywell-device-wizard input::-webkit-outer-spin-button,
.honeywell-device-wizard input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

.honeywell-device-wizard input[type="number"] {
    appearance: textfield;
    -moz-appearance: textfield;
}

.ui.grid>.column:not(.row) {
    padding-bottom: 6rem !important;
}

.holiday-list-container {
    border: 1px solid #ADADAD;
    border-radius: 6px;
    padding: 10px;
    margin-right: 28px;
}

.edit-delete-icon-container {
    float: right;
    display: inline-flex;
}

.new-modified-info-icon {
    padding-right: 6px;
}

.holiday-name-icon {
    display: inline-flex;
    position: relative;
}

.holiday-item {
    height: 36px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 700;
}

.add-holiday-container {
    display: flex;
    justify-content: center;
    color: #1792E5;
}

.add-holiday-icon {
    margin-right: 14px;
}

.add-holiday-button {
    display: flex;
    cursor: pointer;
    margin: 15px 0 15px 0;
}

.country-holidays-container {
    display: flex;
    flex-wrap: wrap;
}

.duct-area-calculator-modal {
    min-width: 360px !important;
}

.import-export-btn-container {
    display: inline-flex;
    position: absolute;
    right: 40px;
}

.export-button {
    color: #505050 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    background: none !important;
    border: 1px solid #505050 !important;
    height: 38px;
    margin: 5px 0 0 10px !important;
}

.add-to-list-button {
    color: #505050 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    border-radius: 4px !important;
    background: none !important;
    border: 1px solid #505050 !important;
    height: 38px;
    margin: 5px 0 0 30px !important;
}

.holiday-list {
    margin-top: 8px;
    padding-right: 4px;
}

@media (min-height: 600px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 165px;
    } 
}

@media (min-height: 690px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 235px;
    } 
}

@media (min-height: 700px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 235px;
    } 
}

@media (min-height: 800px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 375px;
    } 
}

@media (min-height: 900px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 435px;
    } 
}

@media (min-height: 1000px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 560px;
    } 
}

@media (min-height: 1300px) { 
    .holiday-list {
        overflow-y: auto;
        max-height: 800px;
    } 
}

.from-to-date-container {
    display: flex;
    width: 260px;
    justify-content: space-between;
	height: 46px;
}

@media (min-width: 370px) {
    .configure-holiday-modal .tc-component:first-child .ui.input>input {
        width: 205px !important;
    }
}

@media (min-width: 430px) {
    .configure-holiday-modal .tc-component:first-child .ui.input>input {
        width: 380px !important;
    }
}

@media (min-width: 680px) {
    .configure-holiday-modal .tc-component:first-child .ui.input>input {
        width: 440px !important;
    }
}

.slider-input {
    top: -5px;
    input {
        font-family: "Source Sans Pro", SourceSansPro, sans-serif !important;
        height: 30px;
        font-size: 16px;
        border-radius: 4px;
        border: inherit !important;
        padding: 0 24px 0 6px !important;
        background: transparent !important;
        color: inherit !important;
    }
}

.submenu-dot {
    height: 8px;
    width: 8px;
    background-color: red;
    border-radius: 50%;
    margin-left: 6px;
}

.dot {
    height: 8px;
    width: 8px;
    background-color: red;
    border-radius: 50%;
    margin-top: 5px;
    margin-right: 10px;
}

.honeywell-device-wizard .tc-component-group-inline.duct-area-dimension>.tc-component,
.honeywell-device-wizard .tc-component-group-inline.duct-area-dimension>.multiply-sign {
    margin-right: 1rem;
}

.honeywell-device-wizard .tc-component-group-inline.duct-area-dimension>.multiply-sign {
    font-size: xx-large;
    font-weight: bold;
}

.honeywell-device-wizard .tc-component-group-inline.duct-area-dimension {
    align-items: center;
}

.duct-help-icon {
    left: -48px;
    top: -12px;
}

.text-input-icon {
    display: inline-block !important;
    top: 4px;
    left: 12px;
    vertical-align: super !important;
}

.dropdown-input-icon {
    display: inline-block !important;
    top: 6px;
    left: 12px;
    vertical-align: super !important;
}

.label-icon {
    display: inline-block !important;
    left: 8px;
}

.honeywell-device-wizard-home-alert-page {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.honeywell-device-wizard-home-alert-page .primary-message {
    font-size: larger;
    font-weight: 900;
    margin-bottom: 10px;
}