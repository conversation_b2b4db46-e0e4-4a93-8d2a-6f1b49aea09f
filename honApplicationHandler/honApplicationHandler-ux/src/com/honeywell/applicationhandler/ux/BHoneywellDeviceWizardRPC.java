/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON>well.
 */
package com.honeywell.applicationhandler.ux;

import static com.honeywell.applicationhandler.ontology.Const.MAX;
import static com.honeywell.applicationhandler.ontology.Const.MIN;
import static javax.baja.rpc.TransportType.box;
import static javax.baja.rpc.TransportType.web;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.rpc.NiagaraRpc;
import javax.baja.rpc.Transport;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.util.Lexicon;

import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.filegenerator.GlobalStoreFileGenerator;
import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.jobs.BHonWizardValueSaveJob;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.rules.parser.RuleParser;
import com.honeywell.applicationhandler.utils.HonWizardPermissionUtil;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;
import com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase;
import com.tridium.json.JSONArray;
import com.tridium.json.JSONObject;

@NiagaraType
public class BHoneywellDeviceWizardRPC extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ux.BHoneywellDeviceWizardRPC(2979906276)1.0$ @*/
/* Generated Mon Sep 23 15:23:54 CST 2024 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHoneywellDeviceWizardRPC.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    private static final int MAX_SAVE_LOOPS = 50;
    private static final int SLEEP_TIME_MILLIS = 1000;

    private static final String TERMINAL_ASSIGNMENT = "Terminal$20Assignment";
    private static final String SVG_FILE_PATH = "module://honIrmConfig/res/controllerimages/";
    private static Map<BIHoneywellConfigurableDevice, JSONArray> wizardRulesMap = new HashMap<>();
    private static final Lexicon lex = Lexicon.make(BHoneywellDeviceWizardRPC.class);


    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
	public static JSONObject getDynamicStores(Map<String, Object> data, Context cx) {
		JSONObject jsonObject = new JSONObject(data);
		JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
		BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
		BHonWizardRuleStore ruleStore = device.getRuleStore();
		RuleParser parser = new RuleParser(device, true);
		
		String wizardValidationMessage = device.checkAndReturnWizardValidationMessage();
		if (!wizardValidationMessage.isEmpty()) {
			HoneywellDeviceWizardLogger
					.finest("getDynamicStores: Wizard can not be loaded because: " + wizardValidationMessage);
			JSONObject result = new JSONObject();
			result.put("wizardValidationMessage", wizardValidationMessage);
			return result;
		}
		JSONArray wizardRules = new JSONArray();
		BHonWizardRuleParserJob.validateAndGenerateWizardRulesJSON(ruleStore, parser, wizardRules);
		if (!parser.getErrorReports().isEmpty()) {
			// Print only the first error.
			HoneywellDeviceWizardLogger.warning("Error parsing rules: " + parser.getErrorReports().get(0).toString());
			JSONObject result = new JSONObject();
			result.put("wizardValidationMessage", lex.get("HoneywellDeviceWizard.rule.validation.fail"));
			return result;
		}
		wizardRulesMap.put(device, wizardRules);
		
		JSONObject result = readJsonFromComponent(device);
		// Restore the IRM program as part of load sequence
		try {
			device.handleBackEndCallsDuringWizardLoad();
		} catch (Exception e) {
			HoneywellDeviceWizardLogger.severe("Swap in could not be done during wizard load.", e);
		}
		HoneywellDeviceWizardLogger.finest(MessageFormat.format("getDynamicStores result: {0}", result));
		return result;
	}
    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
    /**
     * RPC call during close of honeywell device wizard view/ moving out from honeywell device wizard view
     * @param data
     * @param cx
     */
    public static void closeWizard(Map<String, Object> data, Context cx) {
    	JSONObject jsonObject = new JSONObject(data);
        JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
        BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
    	device.callSwapOutOnCloseOfWizard();
    }
    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
	public static JSONArray getWizardRules(Map<String, Object> data, Context cx) {
		JSONObject jsonObject = new JSONObject(data);
		JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
		BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
		JSONArray result = wizardRulesMap.get(device);
		HoneywellDeviceWizardLogger.finest(MessageFormat.format("getWizardRules result: {0}", result));
		// clean up to avoid memory consumption
		wizardRulesMap.remove(device);
		return result;
	}

    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
    public static String saveDynamicStoreValues(Map<String, Object> data, Context cx) {
        JSONObject jsonObject = new JSONObject(data);
        JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
        JSONObject valueObject = myJsonObject.getJSONObject(Constants.VALUE_OBJECT);
        JSONArray pageOrderArray = new JSONArray();
        if (!myJsonObject.isNull("mainTabIndexes")) {
        	pageOrderArray = myJsonObject.getJSONArray("mainTabIndexes");
        }
        
        BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
        BHonWizardGlobalStore globalStore = device.getGlobalStore();
        BHonWizardValueSaveJob saveJob = new BHonWizardValueSaveJob(device, valueObject, pageOrderArray, globalStore);
        saveJob.submit(null);
        
        int loopCount = 0;
        // wait max 50 secs including save to resp comp and process to controller
        while(saveJob.getJobState().isRunning() && loopCount < MAX_SAVE_LOOPS) {
        	try {
				Thread.sleep(SLEEP_TIME_MILLIS);
			} catch (InterruptedException e) {
				HoneywellDeviceWizardLogger.warning("Save thread interrupted " + e);
			}
        	loopCount++;
        }
        
        return "";
    }

    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
    public static JSONObject getTerminalAssignmentView(Map<String, Object> data, Context cx) throws IOException {
        JSONObject result = new JSONObject();
        JSONObject jsonObject = new JSONObject(data);
        JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
        BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
        if(null == device || null == device.getGlobalStore() || null == device.getGlobalStore().get(TERMINAL_ASSIGNMENT)) {
            return result;
        }
        BComponent terminalComp = (BComponent)device.getGlobalStore().get(TERMINAL_ASSIGNMENT);
        BComponent svgComp = (BComponent) terminalComp.get("svg");
        String svgFile = SVG_FILE_PATH + svgComp.get("svgFile").toString();
        Map<String, List<Double>> textCoordinateMap = new HashMap<>();
        List<Double> viewBox = new ArrayList<>();
        String[] svgSplits = TerminalAssignmentUtil.extractStyleAndContent(svgFile, terminalComp, textCoordinateMap, viewBox);
        if(null == svgSplits){
            return result;
        }
        result.put("style", svgSplits[0]);
        result.put("svgContent", svgSplits[1]);
        result.put("textCoordinateMap", textCoordinateMap);
        result.put("viewBox", viewBox);
        return result;

    }

    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
    public static JSONObject convertUnits(Map<String, Object> data, Context cx) {
        JSONObject jsonObject = new JSONObject(data);
        JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
        BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
        JSONObject measurementTypeObject = myJsonObject.getJSONObject("measurementType");
        if(null == device) {
            return new JSONObject();
        }
        if (null == measurementTypeObject || measurementTypeObject.isEmpty()) {
            return new JSONObject();
        }
        JSONObject valueObject = myJsonObject.getJSONObject(Constants.VALUE_OBJECT);
        if (null == valueObject || valueObject.isEmpty()) {
            return new JSONObject();
        }
        String measurementTypeName = measurementTypeObject.keys().next();

        JSONArray storeArray = valueObject.names();
        JSONObject response = new JSONObject();
        JSONObject respValueObj = getUnitConversionResponse(valueObject, measurementTypeName, storeArray, device, false);
        response.put("measurementType", measurementTypeObject);
        response.put(Constants.VALUE_OBJECT, respValueObj);

        return response;
    }
    
    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
    public static JSONObject convertAirflowUnits(Map<String, Object> data, Context cx) {
        JSONObject jsonObject = new JSONObject(data);
        JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
        JSONObject curAirflowUnit = myJsonObject.getJSONObject("airflowUnit");
        BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
        if (null == curAirflowUnit || curAirflowUnit.isEmpty()) {
            return new JSONObject();
        }
        if(null == device) {
            return new JSONObject();
        }
        
        JSONObject valueObject = myJsonObject.getJSONObject(Constants.VALUE_OBJECT);
        if (null == valueObject || valueObject.isEmpty()) {
            return new JSONObject();
        }
        String currentFlowUnit = curAirflowUnit.keys().next();


        JSONArray storeArray = valueObject.names();

        JSONObject response = new JSONObject();
        JSONObject respValueObj = getUnitConversionResponse(valueObject, getConvertedUnitName(currentFlowUnit), storeArray, device, true);
        response.put("airflowUnit", currentFlowUnit);
        response.put(Constants.VALUE_OBJECT, respValueObj);
        return response;
    }

    /**
     * convert value based on type, type can be measure type or air flow unit
     * @param valueObject
     * @param type
     * @param storeArray
     * @param device
     * @return
     */
    private static JSONObject getUnitConversionResponse(JSONObject valueObject, String type, JSONArray storeArray, BIHoneywellConfigurableDevice device, boolean isAirFlowUnitConversion){
        JSONObject respValueObj = new JSONObject();
        Map<String, BHonWizSelector> selectorWidgetCompOrdMap = createSelectorWidgetCompOrdMap(device);

        for (int i = 0; i < storeArray.length(); i++) {
            String storeName = storeArray.getString(i);
            JSONArray storeValues = valueObject.getJSONArray(storeName);

            JSONArray respJsonArray = new JSONArray();
            respValueObj.put(storeName, respJsonArray);
            for (int j = 0; j < storeValues.length(); j++) {
                JSONObject storeValue = storeValues.getJSONObject(j);
                JSONObject result = UnitConversionUtil.convertUnits(type, isAirFlowUnitConversion, storeValue);
                respJsonArray.put(result);
                if(result.has(MIN) && result.has(MAX) && result != storeValue){
                    BWidgetComponentBase widgetComponent = device.getGlobalStore().getWidgetComponent(storeName, result.getString(Const.NAME));
                    BHonWizSelector selectorByWidgetCompOrd = HoneywellConfigurableDeviceUtil.findSelectorByWidgetCompOrd(device, selectorWidgetCompOrdMap, (BDynamicWidgetComponentBase) widgetComponent);
                    if(null != selectorByWidgetCompOrd){
                        selectorByWidgetCompOrd.setChangedAsUnitConversion(true);
                        selectorByWidgetCompOrd.tags().set(BHonWizardTag.MIN_TAG, BString.make(String.format("%.2f", result.getDouble(MIN))));
                        selectorByWidgetCompOrd.tags().set(BHonWizardTag.MAX_TAG, BString.make(String.format("%.2f", result.getDouble(MAX))));
                        selectorByWidgetCompOrd.setChangedAsUnitConversion(false);

                    }
                }
            }
        }
        return respValueObj;
    }

    /**
     * create a map of wizard component ord to selector widget component
     * @param device
     * @return
     */
    private static Map<String, BHonWizSelector> createSelectorWidgetCompOrdMap(BIHoneywellConfigurableDevice device) {
        Map<String, BHonWizSelector> selectorMap = new HashMap<>();
        List<BHonWizSelector> allHonWizardPointSelectors = HoneywellConfigurableDeviceUtil.getAllHonWizardPointSelectors((BComponent) device);
        for (BHonWizSelector selector : allHonWizardPointSelectors) {
            BComplex parent = selector.getParent();
            if(parent instanceof BIHonWizardPoint) {
                BIHonWizardPoint wizardPoint = (BIHonWizardPoint) parent;
				if (wizardPoint.getWizardComponentOrd() != null && !wizardPoint.getWizardComponentOrd().isNull()
						&& !wizardPoint.getWizardComponentOrd().equals(BOrd.make("")) && !wizardPoint.getWizardComponentOrd().equals(BOrd.DEFAULT)) {
					selectorMap.put(wizardPoint.getWizardComponentOrd().toString(), selector);
				}
            }
        }
        return selectorMap;
    }
    
    @NiagaraRpc(
            permissions = "unrestricted",
            transports = {
                    @Transport(type = web),
                    @Transport(type = box)
            }
    )
    public static JSONObject getPermissionsForLoginUser(Map<String, Object> data, Context cx) {
        JSONObject result = new JSONObject();
        JSONObject jsonObject = new JSONObject(data);
        JSONObject myJsonObject = jsonObject.getJSONObject(Constants.MYJSON);
        BIHoneywellConfigurableDevice device = RPCUtil.getDevice(myJsonObject);
        if(null == device) {
            return result;
        }
        BHonWizardGlobalStore globalStore = device.getGlobalStore();
        BHonWizardRuleStore ruleStore = device.getRuleStore();
        if(null == globalStore || null == ruleStore) {
            return result;
        }
        boolean isReadOnly = HonWizardPermissionUtil.isWizardViewReadonly(device, cx);
        jsonObject.put("isReadOnly", isReadOnly);
        return jsonObject;

    }

    private static JSONObject readJsonFromComponent(BIHoneywellConfigurableDevice device) {
        BHonWizardGlobalStore globalStore = device.getGlobalStore();
        if(null == globalStore) {
            return new JSONObject();
        }

        return GlobalStoreFileGenerator.generateJson(globalStore);
    }
    
    private static String getConvertedUnitName(String currentUnitName) {
    	switch (currentUnitName) {
			case "Cubic Feet Per Minute":
			case "CFM":
			case "CubicFeetPerMinute":
			case "cubicfeetperminute":
			case "CubicFeetPerMinute/CFM":
			case "cfm":
				return "cubic feet per minute";
			case "Cubic Meter Per Hour":
			case "CMH":
			case "CubicMeterPerHour":
			case "cubicmeterperhour":
			case "CubicMeterPerHour/CMH":
			case "cmh":
				return "cubic meters per hour";
			case "Liter Per Second":
			case "LPS":
			case "LiterPerSecond":
			case "literpersecond":
			case "LiterPerSecond/LPS":
			case "lps":
				return "liters per second";
			default:
				return currentUnitName;
		}
    }

}
