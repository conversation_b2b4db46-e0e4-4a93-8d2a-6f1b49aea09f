/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ux;

import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.tridium.json.JSONObject;
import com.tridium.nre.util.tuple.Pair;

import javax.baja.units.BUnit;
import javax.baja.units.BUnitConversion;
import java.util.ArrayList;
import java.util.List;

import static com.honeywell.applicationhandler.ontology.Const.*;

public final class UnitConversionUtil {

    private UnitConversionUtil(){

    }

    /**
     * convert units for the given measure type
     * @param measurementTypeName, measure type name
     * @param storeValue, global store value
     * @return JSON object
     */
    public static JSONObject convertUnits(String measurementTypeName, boolean isAirflowwUnitConversion, JSONObject storeValue){
        JSONObject result = new JSONObject();
        String unitName = getKeyValue(storeValue, UNIT_NAME);
        String highPrecisionValue = getKeyValue(storeValue, HIGH_PRECISION_VALUE);
        String maxValue = getKeyValue(storeValue, HIGH_PRECISION_MAX);
        String minValue = getKeyValue(storeValue, HIGH_PRECISION_MIN);
        String deadband = getKeyValue(storeValue, HIGH_PRECISION_DEAD_BAND);
        String defaultValue = getKeyValue(storeValue, HIGH_PRECISION_DEFAULT_VALUE);
        String propertyName = getKeyValue(storeValue, NAME);
        Double step = storeValue.getDouble(STEP);

        Pair<BUnit, List<String>> pair = null;
        if(!isAirflowwUnitConversion) {
        	pair = convertUnit(propertyName, measurementTypeName, unitName, highPrecisionValue, minValue, maxValue, defaultValue, deadband);
        } else {
        	pair = convertAirFlowUnit(propertyName, measurementTypeName, unitName, highPrecisionValue, minValue, maxValue, defaultValue, deadband);
        }
        if(null != pair) {
            result.put(UNIT_NAME, pair.getFirst().getUnitName());
            result.put(UNIT, pair.getFirst().getSymbol());
            result.put(HIGH_PRECISION_VALUE, pair.getSecond().get(0));
            result.put(HIGH_PRECISION_MIN, pair.getSecond().get(1));
            result.put(HIGH_PRECISION_MAX, pair.getSecond().get(2));
            result.put(HIGH_PRECISION_DEFAULT_VALUE, pair.getSecond().get(3));
            result.put(HIGH_PRECISION_DEAD_BAND, pair.getSecond().get(4));
            if(isNumeric(pair.getSecond().get(0))) {
                result.put(VALUE, Double.parseDouble(pair.getSecond().get(0)));
            }
            if(isNumeric(pair.getSecond().get(3))) {
                result.put(DEFAULT_VALUE, Double.parseDouble(pair.getSecond().get(3)));
            }
            if(isNumeric(pair.getSecond().get(2))) {
                result.put(MAX, Double.parseDouble(pair.getSecond().get(2)));
            }
            if(isNumeric(pair.getSecond().get(1))) {
                result.put(MIN, Double.parseDouble(pair.getSecond().get(1)));
            }
            if(isNumeric(pair.getSecond().get(4))) {
                result.put(DEAD_BAND, Double.parseDouble(pair.getSecond().get(4)));
            }
            if (pair.getFirst().getUnitName().toLowerCase().contains(FAHRENHEIT)){
                result.put(STEP, 1);
            }else if(pair.getFirst().getUnitName().toLowerCase().contains(CELSIUS)){
                result.put(STEP, 0.5);
            }else{
                result.put(STEP, step);
            }
            result.put(NAME, propertyName);
        }else{
            return storeValue;
        }
        return result;
    }

    /**
     * parse key value from store value
     * @param storeValue, global store value
     * @param key, key
     * @return value
     */
    private static String getKeyValue(JSONObject storeValue, String key){
        if(storeValue.has(key)){
            return storeValue.getString(key);
        }
        return "";
    }

    /**
     * check if the string is numeric
     * @param str
     * @return
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * convert unit
     * @param propertyName, property name
     * @param measurementType, measure type
     * @param unitName, unit name
     * @param highPrecisionValue, high precision value
     * @param min, high precision min
     * @param max, high precision max
     * @param defaultValue, high precision default value
     * @param deadBand, high precision dead band
     * @return, pair of BUnit and list of converted values
     */
    private static Pair<BUnit, List<String>> convertUnit(String propertyName, String measurementType, String unitName, String highPrecisionValue, String min, String max, String defaultValue, String deadBand) {
        BUnit sourceUnit = BUnit.getUnit(unitName);
        if(null == sourceUnit) {
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.parseUnit", unitName));
            return null;
        }
        BUnitConversion srcUnitConversion = getSourceUnitConversion(measurementType);
        if (srcUnitConversion.getOrdinal() == BUnitConversion.NONE) {
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.parseMeasurementType", measurementType));
            return null;
        }
        BUnit desiredUnit = srcUnitConversion.getDesiredUnit(sourceUnit);
        if(desiredUnit == sourceUnit){
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.desiredUnitNotFound", unitName, measurementType));
            //converted unit is not defined in unit conversion xml, firstly need to find unit in unit conversion xml, their measure type is same.
            List<BUnit> matchUnits = getMatchUnits(sourceUnit, measurementType);
            if(!matchUnits.isEmpty()){
                BUnit sourceUnitSameDimension = matchUnits.get(0);
                BUnit desiredUnitForNewUnit = srcUnitConversion.getDesiredUnit(sourceUnitSameDimension);

        		String tempHighPrecisionValue = convertInSameDimension(sourceUnit, sourceUnitSameDimension, highPrecisionValue);
        		String tempMin = convertInSameDimension(sourceUnit, sourceUnitSameDimension, min);
        		String tempMax = convertInSameDimension(sourceUnit, sourceUnitSameDimension, max);
        		String tempDefaultValue = convertInSameDimension(sourceUnit, sourceUnitSameDimension, defaultValue);
        		return new Pair<>(desiredUnitForNewUnit, getConvertedValues(propertyName, sourceUnitSameDimension, desiredUnitForNewUnit, tempHighPrecisionValue,
        		         tempMin, tempMax, tempDefaultValue, deadBand));

            }else{
                HoneywellDeviceWizardLogger.finest(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.desiredUnitWithSameDimensionNotFoundInUnitConversion", unitName, measurementType));
            }
        } else {

            return new Pair<>(desiredUnit, getConvertedValues(propertyName, sourceUnit, desiredUnit, highPrecisionValue, min, max, defaultValue, deadBand));

        }
        return null;
    }
    
    private static Pair<BUnit, List<String>> convertAirFlowUnit(String propertyName, String targetUnitName, String unitName, String highPrecisionValue, String min, String max, String defaultValue, String deadBand) {
		BUnit sourceUnit = BUnit.getUnit(unitName);
		if(null == sourceUnit) {
			HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.parseUnit", unitName));
			return null;
		}
		BUnit toUnit = BUnit.getUnit(targetUnitName);
		
		if(toUnit == sourceUnit){
			HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.desiredAirFlowUnitNotFound", unitName, targetUnitName));
			return null;
		} else {
			return new Pair<>(toUnit, getConvertedValues(propertyName, sourceUnit, toUnit, highPrecisionValue, min, max, defaultValue, deadBand));
		}
	}

    /**
     * convert value in same dimension
     * @param sourceUnit, source unit
     * @param desiredUnit, desired unit
     * @param value, value
     * @return converted value
     */
    private static  String convertInSameDimension(BUnit sourceUnit, BUnit desiredUnit, String value) {
        if(!isNumeric(value)){
            return value;
        }
        return String.valueOf(sourceUnit.convertTo(desiredUnit, Double.parseDouble(value)));
    }

    private static List<String> getConvertedValues(String propertyName, BUnit srcUnit, BUnit toUnit, String highPrecisionValue, String min, String max, String defaultValue, String deadBand) {
        List<String> results = new ArrayList<>();
        if(!isNumeric(highPrecisionValue)){
            results.add(INVALID_UNIT_CONVERSION_VALUE);
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.highPrecisionValueNotNumeric", propertyName, highPrecisionValue));
        }else {
            results.add(String.valueOf(srcUnit.convertTo(toUnit, Double.parseDouble(highPrecisionValue))));
        }
        if(!isNumeric(min)){
            results.add(INVALID_UNIT_CONVERSION_VALUE);
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.minNotNumeric", propertyName, min));
        }else {
            results.add(String.valueOf(srcUnit.convertTo(toUnit, Double.parseDouble(min))));
        }
        if(!isNumeric(max)) {
            results.add(INVALID_UNIT_CONVERSION_VALUE);
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.maxNotNumeric", propertyName, max));

        }else{
            results.add(String.valueOf(srcUnit.convertTo(toUnit, Double.parseDouble(max))));
        }
        if(!isNumeric(defaultValue)){
            results.add(INVALID_UNIT_CONVERSION_VALUE);
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.defaultValueNotNumeric", propertyName, defaultValue));
        }else {
            results.add(String.valueOf(srcUnit.convertTo(toUnit, Double.parseDouble(defaultValue))));
        }
        if(!isNumeric(deadBand)) {
            results.add(INVALID_UNIT_CONVERSION_VALUE);
            HoneywellDeviceWizardLogger.warning(LexiconUtil.getLexicon().getText("HoneywellDeviceWizard.UnitConversionUtil.deadBandNotNumeric", propertyName, deadBand));
        }else{
            if(toUnit.getDifferentialUnit().getSymbol().equals(toUnit.getSymbol())) {
                results.add(String.valueOf(srcUnit.convertTo(toUnit, Double.parseDouble(deadBand))));
            }else{
                results.add(String.valueOf(srcUnit.getDifferentialUnit().convertTo(toUnit.getDifferentialUnit(), Double.parseDouble(deadBand))));
            }
        }
        return results;

    }

    /**
     * get unit conversion object
     * @param targetMeasurementType, measure type
     * @return unit conversion object
     */
    private static BUnitConversion getSourceUnitConversion(String targetMeasurementType){
        if(targetMeasurementType.toLowerCase().contains(IMPERIAL) || targetMeasurementType.toLowerCase().contains(ENGLISH)){
            return BUnitConversion.english;
        } else if (targetMeasurementType.toLowerCase().contains(METRIC)){
            return BUnitConversion.metric;
        } else {
            return BUnitConversion.none;
        }
    }

    /**
     * get measure type ordinal
     * @param measurementType, measure type
     * @return ordinal
     */
    private static int getMeasurementTypeOrdinal(String measurementType){
        if(measurementType.toLowerCase().contains(IMPERIAL) || measurementType.toLowerCase().contains(ENGLISH)){
            return BUnitConversion.ENGLISH;
        } else if (measurementType.toLowerCase().contains(METRIC)){
            return BUnitConversion.METRIC;
        } else {
            return BUnitConversion.NONE;
        }
    }

    /**
     * get match units in same dimension
     * @param unit, unit
     * @param measurementType, measure type
     * @return unit list
     */
    private static List<BUnit> getMatchUnits(BUnit unit, String measurementType) {
        int measurementTypeOrdinal = getMeasurementTypeOrdinal(measurementType);
        List<BUnitConversion.Entry> entries = BUnitConversion.getEntries();
        List<BUnit> results = new ArrayList<>();
        for (BUnitConversion.Entry entry : entries) {
            if(measurementTypeOrdinal == BUnitConversion.ENGLISH){
                BUnit english = entry.getEnglish();
                if(english.getDimension().equals(unit.getDimension())){
                    results.add(english);
                }

            } else if(measurementTypeOrdinal == BUnitConversion.METRIC){
                BUnit metric = entry.getMetric();
                if(metric.getDimension().equals(unit.getDimension())){
                    results.add(metric);
                }

            }
        }
        return results;
    }
}
