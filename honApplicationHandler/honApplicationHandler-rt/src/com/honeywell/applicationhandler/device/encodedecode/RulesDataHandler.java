/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.device.encodedecode;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

import javax.baja.sys.BFacets;
import javax.baja.sys.BString;

import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.utils.WizardLogger;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jul 2, 2025
 */
public class RulesDataHandler extends DataHandler {
	
    // Constants for buffer sizes
    private static final int BUFFER_SIZE = 1024;
	
    @Override
    public Map<Object, Integer> collectRepeatedValues(Object data) {
    	//Default Implementation when no value to collected
        return new HashMap<>();
    }

    @Override
	public void encode(ByteArrayBuilder byteArrayBuilder, Object data, TagMapping tagMapping) {
		if (data instanceof BHonWizardRuleStore) {
			WizardLogger.logInfo(lex.get("honWizardJob.encode.rules.start"));

			Map<String, String> rules = ((BHonWizardRuleStore) data).getAllRules();
			ByteArrayBuilder rulesBuilder = new ByteArrayBuilder();

			for (Map.Entry<String, String> entry : rules.entrySet()) {
				String ruleName = entry.getKey();
				String ruleContent = entry.getValue();

				// Compress the rule content
				byte[] compressedRule = compressString(ruleContent);

				// Encode the rule name and compressed content
				rulesBuilder.addString(ruleName);
				rulesBuilder.addInt(compressedRule.length);
				rulesBuilder.addBytes(compressedRule, 0, compressedRule.length);

				WizardLogger.logFiner(lex.get("honWizardJob.encode.rules.rule"), ruleName, compressedRule.length);
			}

			// Add the size of the rules and the rules themselves
			byteArrayBuilder.addInt(rulesBuilder.getLength());
			byteArrayBuilder.addBytes(rulesBuilder.toByteArray(), 0, rulesBuilder.getLength());

			WizardLogger.logInfo(lex.get("honWizardJob.encode.rules.completed"));
		}
	}

    @Override
    public void decode(ByteArrayReader byteArrayReader, Map<Integer, Object> repeatedTagMappings, TagMapping tagMapping, BIHoneywellConfigurableDevice device) {
    	BHonWizardRuleStore ruleStore = device.getRuleStore();
        WizardLogger.logInfo(lex.get("honWizardJob.decode.rules.start"));

        int rulesSize = byteArrayReader.readInt(); // Read the size of the rules
        int currentPos = byteArrayReader.currentPos();

        ruleStore.doClearAllRules();
        
        while (byteArrayReader.currentPos() < (currentPos + rulesSize)) {
            String ruleName = byteArrayReader.readString(); // Decode the rule name
            int compressedRuleSize = byteArrayReader.readInt(); // Decode the size of the compressed rule
            byte[] compressedRule = byteArrayReader.readBytes(compressedRuleSize); // Decode the compressed rule

            // Decompress the rule content
            String ruleContent = decompressString(compressedRule);

            // Add the rule to the RuleStore
			ruleStore.add(ruleName, BString.make(ruleContent), 0,
					BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);

            WizardLogger.logFiner(lex.get("honWizardJob.decode.rules.rule"), ruleName, ruleContent);
        }

        WizardLogger.logInfo(lex.get("honWizardJob.decode.rules.completed"));
    }
    
    private byte[] compressString(String data) {
        try {
            byte[] input = data.getBytes(StandardCharsets.UTF_8);
            Deflater deflater = new Deflater();
            deflater.setInput(input);
            deflater.finish();

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(input.length);
            byte[] buffer = new byte[BUFFER_SIZE];
            while (!deflater.finished()) {
                int compressedDataLength = deflater.deflate(buffer);
                byteArrayOutputStream.write(buffer, 0, compressedDataLength);
            }
            deflater.end();
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            WizardLogger.logSevere(lex.getText("honWizardJob.encode.rules.compress.error", e.getMessage()));
            throw new RuntimeException("Error compressing string", e);
        }
    }
    
    private String decompressString(byte[] compressedData) {
        try {
            Inflater inflater = new Inflater();
            inflater.setInput(compressedData);

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(compressedData.length);
            byte[] buffer = new byte[BUFFER_SIZE];
            while (!inflater.finished()) {
                int decompressedDataLength = inflater.inflate(buffer);
                byteArrayOutputStream.write(buffer, 0, decompressedDataLength);
            }
            inflater.end();
            return byteArrayOutputStream.toString(StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            WizardLogger.logSevere(lex.getText("honWizardJob.decode.rules.decompress.error", e.getMessage()));
            throw new RuntimeException("Error decompressing string", e);
        }
    }
}