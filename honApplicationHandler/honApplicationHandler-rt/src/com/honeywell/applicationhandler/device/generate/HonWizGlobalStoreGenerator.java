/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */
package com.honeywell.applicationhandler.device.generate;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BHonWizardSlotDetails;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumSchedule;
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.exceptions.GlobalStoreGenerationExcpetion;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;
import com.honeywell.applicationhandler.utils.LexiconUtil;
import com.honeywell.applicationhandler.widgetcomponents.BAirflowUnit;
import com.honeywell.applicationhandler.widgetcomponents.BCheckbox;
import com.honeywell.applicationhandler.widgetcomponents.BDropdown;
import com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator;
import com.honeywell.applicationhandler.widgetcomponents.BMeasurementType;
import com.honeywell.applicationhandler.widgetcomponents.BNumberInput;
import com.honeywell.applicationhandler.widgetcomponents.BRadioButton;
import com.honeywell.applicationhandler.widgetcomponents.BRangeSlider;
import com.honeywell.applicationhandler.widgetcomponents.BSchedulePageWidget;
import com.honeywell.applicationhandler.widgetcomponents.BSelectButton;
import com.honeywell.applicationhandler.widgetcomponents.BSelectWidget;
import com.honeywell.applicationhandler.widgetcomponents.BSingleSlider;
import com.honeywell.applicationhandler.widgetcomponents.BSwitchButton;
import com.honeywell.applicationhandler.widgetcomponents.BTextInput;
import com.honeywell.applicationhandler.widgetcomponents.BTimeZone;
import com.honeywell.applicationhandler.widgetcomponents.BWidgetComponentBase;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.data.BIDataValue;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.schedule.BEnumSchedule;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BInteger;
import javax.baja.sys.BLong;
import javax.baja.sys.BNumber;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.tag.Id;
import javax.baja.tag.Tag;
import javax.baja.units.BUnit;
import javax.baja.util.Lexicon;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.honeywell.applicationhandler.common.Constants.*;


/**
 * The HonWizGlobalStoreGenerator class provides utility methods for configuring and managing
 * Honeywell devices within the application. It handles the generation of global store configurations,
 * slot details, and updates for Honeywell devices. This class interacts with various components and
 * selectors to build and update the device configuration.

 * <p>Key functionalities include:</p>
 * <ul>
 *   <li>Generating a new global store configuration for the device.</li>
 *   <li>Sorting slot paths based on order tags.</li>
 *   <li>Collecting slot paths by tabs and groups.</li>
 *   <li>Generating component structures from selectors.</li>
 *   <li>Handling various widget types such as sliders, checkboxes, and dropdowns.</li>
 * </ul>
 *
 * <p>Dependencies:</p>
 * <ul>
 *   <li>BIHoneywellConfigurableDevice: Represents the Honeywell device being configured.</li>
 *   <li>BHonWizardGlobalStore: Represents the global store configuration for the device.</li>
 *   <li>BHonWizardSlotDetails: Holds details about the slots in the device configuration.</li>
 *   <li>BHonWizSelector: Represents a selector used in the wizard configuration.</li>
 *   <li>BHonWizSlotSelector: Represents a slot selector used in the wizard configuration.</li>
 *   <li>BHonWizPointSelector: Represents a point selector used in the wizard configuration.</li>
 *   <li>Various widget components such as BCheckbox, BSingleSlider, BRangeSlider, etc.</li>
 *   <li>HoneywellConfigurableDeviceUtil: static utility class that provides some utility methods.</li>
 * </ul>
 *
 * <p>Usage:</p>
 * <pre>
 * {@code
 * BIHoneywellConfigurableDevice device = ...;
 * HonWizGlobalStoreGenerator generator = new HonWizGlobalStoreGenerator(device);
 * generator.generate();
 * }
 * </pre>
 *
 */
/**
 * <AUTHOR> Sun
 */
public class HonWizGlobalStoreGenerator {
    static final Lexicon lex = LexiconUtil.getLexicon();
    BIHoneywellConfigurableDevice device;
    BHonWizardGlobalStore existingGlobalStore;
    BHonWizardSlotDetails slotDetails;
    Map<BOrd, String> updatedSlotDetails;

    public HonWizGlobalStoreGenerator(BIHoneywellConfigurableDevice device) {
        this.device = device;
        slotDetails = new BHonWizardSlotDetails();
        updatedSlotDetails = new HashMap<>();
    }

    public BHonWizardSlotDetails getSlotDetails() {
        return slotDetails;
    }

    public Map<BOrd, String> getUpdatedSlotDetails() {
        return updatedSlotDetails;
    }

    public void generate(Context context) throws GlobalStoreGenerationExcpetion {
        Map<String, List<SlotPath>> pageNameToSlotPathList = new HashMap<>();
        existingGlobalStore = (BHonWizardGlobalStore) device.getGlobalStore().newCopy();
        generatePagesDataFromSelectors(pageNameToSlotPathList);
        BHonWizardGlobalStore newGlobalStore = createNewGlobalStore();
        device.setGlobalStore(newGlobalStore);
        List<String> pageNames = new ArrayList<>();

        try {
            Iterator<Map.Entry<String, List<SlotPath>>> pageIterator = pageNameToSlotPathList.entrySet().iterator();
            while(pageIterator.hasNext()) {
                Map.Entry<String, List<SlotPath>> pageEntry = pageIterator.next();
                List<SlotPath> slotPathList = pageEntry.getValue();
                HoneywellConfigurableDeviceUtil.sortByOrder((BComponent)device, slotPathList);
                String pageName = pageEntry.getKey();
                Map<String, Map<String, List<SlotPath>>> tabNameToGrpNameToSlotPathLists = collectSlotPathByTabsGroups(slotPathList);

                generateComponentStruct(pageName, tabNameToGrpNameToSlotPathLists, newGlobalStore);
                pageNames.add(pageName);
                assignPermissionToNewGlobalStore(existingGlobalStore, newGlobalStore);
            }

            generateTerminalAssignment(pageNames, newGlobalStore);
            checkAndAddPageOrder(pageNames, newGlobalStore);
        } catch (Exception e) {
        	String msg = lex.getText("HonWizGlobalStoreGenerator.exception.occurred", Arrays.toString(e.getStackTrace()));
            HoneywellDeviceWizardLogger.severe(msg);
            // Revert to existing global store.
            device.setGlobalStore(existingGlobalStore);
            throw new GlobalStoreGenerationExcpetion(msg);
        }
        if(null != slotDetails) {
            device.setHonWizardSlotDetails(slotDetails);
        }
        if(null != updatedSlotDetails) {
            device.processHonWizardSlotUpdates(updatedSlotDetails);
        }
    }

    public BComponent generatePageComponent(String pageName) throws GlobalStoreGenerationExcpetion {
        List<SlotPath> slotPathList = generatePageSlotPathsFromSelectors(pageName);
        HoneywellConfigurableDeviceUtil.sortByOrder((BComponent)device, slotPathList);
        Map<String, Map<String, List<SlotPath>>> tabNameToGrpNameToSlotPathLists = collectSlotPathByTabsGroups(slotPathList);

        return generateComponentStruct(pageName, tabNameToGrpNameToSlotPathLists, device.getGlobalStore());
    }

    private List<SlotPath> generatePageSlotPathsFromSelectors(String pageName) {
        Map<String, Object> tagValues = new HashMap<>();
        tagValues.put("Page", pageName);
        List<BHonWizSelector> selectors = HoneywellConfigurableDeviceUtil.getHonWizSelectorsWithTags((BComponent) device, tagValues);

        List<SlotPath> slotPathList = new ArrayList<>();
        for(BHonWizSelector selector : selectors) {
            slotPathList.add(selector.getSlotPath());
        }

        return slotPathList;
    }

    /**
     * assign wizard permission for new global store with existing global store permission
     * @param existingGlobalStore, existing global store
     * @param newGlobalStore, new global store
     */
    private void assignPermissionToNewGlobalStore(BHonWizardGlobalStore existingGlobalStore, BHonWizardGlobalStore newGlobalStore) {
        if (existingGlobalStore == null || newGlobalStore == null) {
            return;
        }
        newGlobalStore.setCategoryMask(existingGlobalStore.getCategoryMask(), null);
    }

    private void generatePagesDataFromSelectors(Map<String,List<SlotPath>> pageNameToSlotPathList) {
        List<BHonWizSelector> selectors = HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors((BComponent) device);
        for(BHonWizSelector selector : selectors) {

            Iterator<Tag> itr = selector.tags().iterator();
            while (itr.hasNext()) {
                Tag t = itr.next();
                Id tagId = t.getId();
                if(tagId.equals(BHonWizardTag.PAGE_TAG)) {
                    BIDataValue tagValue = t.getValue();
                    collectSlotPath(tagValue.toString(), pageNameToSlotPathList, selector);
                }
            }
        }
    }

    private BComponent generateComponentStruct(String pageName, Map<String, Map<String,List<SlotPath>>> tabNameToGrpNameToSlotPathList, BHonWizardGlobalStore newGlobalStore) throws GlobalStoreGenerationExcpetion {
        BComponent pageComponent = new BComponent();

        if(null == newGlobalStore.get(SlotPath.escape(pageName))) {
        	newGlobalStore.add(SlotPath.escape(pageName), pageComponent);
        } else {
        	pageComponent = (BComponent) newGlobalStore.get(SlotPath.escape(pageName));
        }

        Iterator<Map.Entry<String, Map<String, List<SlotPath>>>> iterator = tabNameToGrpNameToSlotPathList.entrySet().iterator();

        while(iterator.hasNext()) {
            Map.Entry<String, Map<String, List<SlotPath>>> entry = iterator.next();
            String tabName = entry.getKey();
            Map<String, List<SlotPath>> grpNameToSlotPathList = entry.getValue();

            Iterator<Map.Entry<String, List<SlotPath>>> grpIterator = grpNameToSlotPathList.entrySet().iterator();

            while(grpIterator.hasNext()) {
                Map.Entry<String, List<SlotPath>> grpEntry = grpIterator.next();
                String grpName = grpEntry.getKey();
                List<SlotPath> slotPathList = grpEntry.getValue();

                BComponent headingComponent = null;
                if(!grpName.isEmpty()) {
                    String headingLabel = grpName;
                    String headingItem = SlotPath.escape(grpName);

                    //First heading
                    headingComponent = new BComponent();
                    headingComponent.add("label", BString.make(headingLabel));
                    headingComponent.add("visible", BBoolean.make(true));
                    headingComponent.add("componentType", BString.make(HEADING_LABEL));
                    headingComponent.add("itemsInGroup", BString.make(""));

                    if(!tabName.isEmpty()) {
                        headingComponent.add("tabInPage",  BString.make(tabName));
                    }

                    pageComponent.add(SlotPath.unescape(headingItem)+"?", headingComponent);
                    headingComponent.add("itemName",  BString.make(headingComponent.getName()));
                }

                for(int i=0; i<slotPathList.size(); i++) {
                    SlotPath sp = slotPathList.get(i);
                    BOrd ord = BOrd.make(sp.toString());
                    BComponent cp = (BComponent)ord.get((BComponent) device);

                    if(cp instanceof BHonWizPointSelector) {
                        generateWidgetComponentStructureFromPointSelector((BHonWizPointSelector)cp, pageComponent, headingComponent);
                    }
                    else if(cp instanceof BHonWizSlotSelector) {
                        //generate component structure for slot
                        generateWidgetComponentStructureFromSlotSelector((BHonWizSlotSelector)cp, pageComponent, headingComponent);
                        HoneywellDeviceWizardLogger.info(lex.getText("HonWizGlobalStoreGenerator.BHonWizSlotSelector.found", cp.getSlotPath().toString()));
                    }

                }
            }
        }

        checkAndCreateWidgetCompAndSlotSelectorsForDuctAreaCalculator(pageComponent);

        return pageComponent;
    }

    private void generateWidgetComponentStructureFromSlotSelector(BHonWizSlotSelector selector, BComponent generatedTargetComponent, BComponent grpComponent) throws GlobalStoreGenerationExcpetion {
        String srcPropertyName = selector.getSourcePropertyNameString();
        BComponent parentComponent = (BComponent) selector.getParent();
        BValue srcValue = parentComponent.get(srcPropertyName);
        String widgetType = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.WGTTYPE_TAG);
        
        if((srcValue == null || parentComponent == null)) {
        	String msg = lex.getText("HonWizGlobalStoreGenerator.generateWidgetComponentStructureFromSlotSelector.failed", selector.getSlotPath().toString());
            HoneywellDeviceWizardLogger.severe(msg);
            throw new GlobalStoreGenerationExcpetion(msg);
        }

        BOrd commonSlotPath = BOrd.make(parentComponent.getSlotPath().toString() + PATH_LIMITER + srcPropertyName);
        

        String label = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.NAME_TAG);
        String name = srcPropertyName;
        if(label == null) {
            label = name;
        }

        String inline = "";
        Double order = HoneywellConfigurableDeviceUtil.getOrderFromTag((BComponent) device, selector.getSlotPath());
        if(Math.floor(order) != order) {
            inline = String.valueOf(Math.floor(order));
        }

        boolean readOnly = false;
        Boolean readOnlyTagValue = HoneywellConfigurableDeviceUtil.getBooleanTagValueOfComponent(selector, BHonWizardTag.READ_ONLY_TAG);
        if(readOnlyTagValue != null) {
            readOnly = readOnlyTagValue;
        }

       
        String tabInPage = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.TAB_IN_PAGE_TAG);

        BWidgetComponentBase widgetBaseComp = null;

        String minValueStr = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.MIN_TAG);
        String maxValueStr = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.MAX_TAG);

        Double minValue = minValueStr == null ? null : Double.valueOf(minValueStr);
        Double maxValue = maxValueStr == null ? null : Double.valueOf(maxValueStr);
        String help = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.HELP_TAG);
        if(CHECKBOX_GROUP.equals(widgetType)) {
            if(srcValue instanceof BInteger || srcValue instanceof BLong) {
                int value = 0;
                if(srcValue instanceof BInteger) {
                    value = ((BInteger) srcValue).getInt();
                }
                else if(srcValue instanceof BLong) {
                    value = ((BLong) srcValue).getInt();
                }
                int defaultValue = value;

                BEnumRange options = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);
                widgetBaseComp = BCheckbox.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, options, commonSlotPath);
                if(widgetBaseComp == null) {
					String msg = lex.getText("HonWizGlobalStoreGenerator.generate.checkbox.failed", commonSlotPath.toString());
					HoneywellDeviceWizardLogger.severe(msg);
					throw new GlobalStoreGenerationExcpetion(msg);
                }
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
            	String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "checkbox group", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(SINGLE_SLIDER.equals(widgetType)) {
            if(srcValue instanceof BNumber) {

                if(minValue == null || maxValue == null) {
                    String msg = lex.getText("HonWizGlobalStoreGenerator.tagvalue.invalid", "min/max", commonSlotPath);
                    HoneywellDeviceWizardLogger.severe(msg);
                    throw new GlobalStoreGenerationExcpetion(msg);
                }

                double value = ((BNumber) srcValue).getDouble();
                double defaultValue = value;
                BUnit unit = (BUnit) parentComponent.getProperty(srcPropertyName).getFacets().get(BFacets.UNITS);
                double step = 1;
                int precision = 0;
                if(null != unit) {
					step = HoneywellConfigurableDeviceUtil.getStep(unit, selector);
					precision = HoneywellConfigurableDeviceUtil.getPrecision(unit, step);
				}
                String color = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.COLOR_TAG);

                color = com.honeywell.applicationhandler.validation.Constants.getColorCodeForColor(color);
        		String unitGroup = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG) != null
        				? HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG)
        				: "";
                widgetBaseComp = BSingleSlider.make(name, value, defaultValue, unit, step, precision, label, color, tabInPage, help, minValue, maxValue, inline, readOnly,
                        commonSlotPath, String.valueOf(value), String.valueOf(minValue), String.valueOf(maxValue), String.valueOf(defaultValue), unitGroup);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);

            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "single slider", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(RANGE_SLIDER.equals(widgetType)) {
            if(srcValue instanceof BNumber) {
                if(minValue == null || maxValue == null) {
                    String msg = lex.getText("HonWizGlobalStoreGenerator.tagvalue.invalid", "min/max", commonSlotPath);
                    HoneywellDeviceWizardLogger.severe(msg);
                    throw new GlobalStoreGenerationExcpetion(msg);
                }

                String color = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.COLOR_TAG);
                color = com.honeywell.applicationhandler.validation.Constants.getColorCodeForColor(color);
                String belong = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.BELONG_TAG);
                String role = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.ROLE_TAG);

                Double deadbandValue = null;

                try {
                    String deadbandValueStr = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.DEADBAND_TAG);
                    deadbandValue = deadbandValueStr == null || deadbandValueStr.isEmpty() ? 0d : Double.valueOf(deadbandValueStr);
                }
                catch (Exception ex) {
                    String msg = lex.getText("HonWizGlobalStoreGenerator.tagvalue.invalid", "deadband", commonSlotPath.toString());
                    HoneywellDeviceWizardLogger.severe(msg);
                    throw new GlobalStoreGenerationExcpetion(msg);
                }

                double value = ((BNumber) srcValue).getDouble();
                double defaultValue = value;
                BUnit unit = (BUnit) parentComponent.getProperty(srcPropertyName).getFacets().get(BFacets.UNITS);

                double step = 1;
                int precision = 0;
                if(null != unit) {
					step = HoneywellConfigurableDeviceUtil.getStep(unit, selector);
					precision = HoneywellConfigurableDeviceUtil.getPrecision(unit, step);
				}

        		String unitGroup = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG) != null
        				? HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG)
        				: "";
                widgetBaseComp = BRangeSlider.make(name, value, defaultValue, unit, step, precision, label, color, tabInPage, help, minValue, maxValue, deadbandValue,
                        inline, readOnly, belong, role, commonSlotPath, String.valueOf(value), String.valueOf(minValue),
                        String.valueOf(maxValue), String.valueOf(defaultValue), String.valueOf(deadbandValue), unitGroup);
                if(widgetBaseComp != null) {
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
                }
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "range slider", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(NUMBER_INPUT.equals(widgetType)) {
            if(srcValue instanceof BNumber) {
                if(minValue == null || maxValue == null) {
                    String msg = lex.getText("HonWizGlobalStoreGenerator.tagvalue.invalid", "min/max", commonSlotPath);
                    HoneywellDeviceWizardLogger.severe(msg);
                    throw new GlobalStoreGenerationExcpetion(msg);
                }

                double value = ((BNumber) srcValue).getDouble();
                double defaultValue = value;
                BUnit unit = (BUnit) parentComponent.getProperty(srcPropertyName).getFacets().get(BFacets.UNITS);

                double step = 1;
                int precision = 0;
                if(null != unit) {
					step = HoneywellConfigurableDeviceUtil.getStep(unit, selector);
					precision = HoneywellConfigurableDeviceUtil.getPrecision(unit, step);
				}

        		String unitGroup = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG) != null
        				? HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG)
        				: "";
                widgetBaseComp = BNumberInput.make(name, value, defaultValue, unit, step, precision, label, tabInPage, help, minValue, maxValue, inline, readOnly,
                        commonSlotPath, String.valueOf(value),String.valueOf(minValue), String.valueOf(maxValue), String.valueOf(defaultValue), unitGroup);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "number input", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(TEXT_INPUT.equals(widgetType)) {
            String value;
            String defaultValue;
            if(srcValue instanceof BEnum) {
                value = srcValue.toString();
                defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);
                widgetBaseComp = BTextInput.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, commonSlotPath, options);
            } else {
                value = srcValue.toString();
                defaultValue = value;
                widgetBaseComp = BTextInput.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, commonSlotPath, null);
            }

            addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
        }
        else if(SWITCH_BUTTON.equals(widgetType)) {
            if(srcValue instanceof BBoolean) {
                boolean value = ((BBoolean) srcValue).getBoolean();
                boolean defaultValue = value;

                widgetBaseComp = BSwitchButton.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "switch button", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(RADIO_BUTTON_GROUP.equals(widgetType)) {
            if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;

                widgetBaseComp = BRadioButton.make(name, value, defaultValue, label, options, tabInPage, help, inline, readOnly, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "radio button group", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(SELECT_BUTTON.equals(widgetType)) {
            if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;

                widgetBaseComp = BSelectButton.make(name, value, defaultValue, label, options, tabInPage, help, inline, readOnly, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "select button", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(SELECT_WIDGET.equals(widgetType)) {
            if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;
                BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);

                widgetBaseComp = BSelectWidget.make(name, value, defaultValue, label, options, tagRange, tabInPage, help, inline, readOnly, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "select widget", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(MEASUREMENT_TYPE.equals(widgetType)) {
            if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;
                //BEnumRange tagRange = getOptionsFromTag(selector);

                widgetBaseComp = BMeasurementType.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, options, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "measurementType", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }

        }
        else if(AIRFLOW_UNIT.equals(widgetType)) {
            if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;

                widgetBaseComp = BAirflowUnit.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, options, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "airFlowUnit widget", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }

        }
        else if(DROPDOWN.equals(widgetType)) {
            if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;

                widgetBaseComp = BDropdown.make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, options, commonSlotPath);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "dropdown", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(DUCT_AREA_CALCULATOR_TYPE.equals(widgetType)) {
            String belong = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.BELONG_TAG);
            String role = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.ROLE_TAG);

            if(srcValue instanceof BNumber) {
                double value = ((BNumber) srcValue).getDouble();
                double defaultValue = value;
                BUnit unit = (BUnit) parentComponent.getProperty(srcPropertyName).getFacets().get(BFacets.UNITS);

                widgetBaseComp = BDuctAreaCalculator.make(name, value, defaultValue, label, tabInPage, help, inline, belong, role, readOnly, unit, null, null, commonSlotPath);
                BComponent tempSelectorDetails = new BComponent();
                for(Property prop : selector.getDynamicPropertiesArray()) {
                	tempSelectorDetails.add(prop.getName(), selector.get(prop), Flags.METADATA);
                }
                ((BDuctAreaCalculator) widgetBaseComp).setAssocaitedSelector(tempSelectorDetails);
                if(widgetBaseComp != null) {
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, role.equals("area") ? grpComponent : null);
                }
            }
            else if(srcValue instanceof BEnum) {
                int value = ((BEnum) srcValue).getOrdinal();
                int defaultValue = value;
                BEnumRange options = HoneywellConfigurableDeviceUtil.getEnumRange(parentComponent, srcPropertyName, srcValue);;
                if(options.equals(BEnumRange.DEFAULT)) {
                	options = (BEnumRange) parentComponent.getSlotFacets(parentComponent.getSlot(srcPropertyName)).get(BFacets.RANGE);
    			}
                BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);

                widgetBaseComp = BDuctAreaCalculator.make(name, value, defaultValue, label, tabInPage, help, inline, belong, role, readOnly, null, options, tagRange, commonSlotPath);
                if(widgetBaseComp != null) {
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, role.equals("area") ? grpComponent : null);
                }
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.slottype.notsupported", "duct area calculator", commonSlotPath.toString());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        }
        else if(widgetType.equals(TIMEZONE_TYPE)) {
        	BTimezoneEnum value = (BTimezoneEnum) srcValue;
            widgetBaseComp = BTimeZone.make(name, value, label, tabInPage, help, inline, readOnly, commonSlotPath);
            addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
        }
        if(null != widgetBaseComp) {
            String srcSlotPath = parentComponent.getSlotPath().toString() + PATH_LIMITER + srcPropertyName;
            if (null == slotDetails.get(SlotPath.escape(widgetBaseComp.getHandleOrd().toString()))) {
                slotDetails.add(SlotPath.escape(widgetBaseComp.getHandleOrd().toString()), BString.make(srcSlotPath));
            } else {
                slotDetails.set(SlotPath.escape(widgetBaseComp.getHandleOrd().toString()), BString.make(srcSlotPath));
            }
            updatedSlotDetails.put(widgetBaseComp.getHandleOrd(), srcSlotPath);
        }
    }

    private void generateWidgetComponentStructureFromPointSelector(BHonWizPointSelector selector, BComponent generatedTargetComponent, BComponent grpComponent) throws GlobalStoreGenerationExcpetion {
        BComponent srcComponent = (BComponent) selector.getParent();

        String label = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.NAME_TAG);
		String name = srcComponent.getName();
        BWidgetComponentBase widgetBaseComp = null;
        if(label == null) {
            label = name;
        }

        String inline = "";
        Double order = HoneywellConfigurableDeviceUtil.getOrderFromTag((BComponent) device, selector.getSlotPath());
        if(Math.floor(order) != order) {
            inline = String.valueOf(Math.floor(order));
        }

        boolean readOnly = false;
        Boolean readOnlyTagValue = HoneywellConfigurableDeviceUtil.getBooleanTagValueOfComponent(selector, BHonWizardTag.READ_ONLY_TAG);
        if(readOnlyTagValue != null) {
            readOnly = readOnlyTagValue;
        }

        String help = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.HELP_TAG);

        if(srcComponent instanceof BIHonWizardNumericPoint) {
            String widgetType = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.WGTTYPE_TAG);
            String tabInPage = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.TAB_IN_PAGE_TAG);
            BBacnetObjectIdentifier bacnetObjectId = ((BIHonWizardNumericPoint) srcComponent).getBacnetObjectId();
            if(CHECKBOX_GROUP.equals(widgetType)) {
                BEnumRange options = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);
                widgetBaseComp = BCheckbox.make(srcComponent, name, label, tabInPage, help, inline, readOnly, options, bacnetObjectId);
                if(widgetBaseComp == null) {
                    String msg = lex.get("HonWizGlobalStoreGenerator.generate.checkbox.failed.forPoint");
                    HoneywellDeviceWizardLogger.severe(msg);
                    throw new GlobalStoreGenerationExcpetion(msg);
                }
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(DUCT_AREA_CALCULATOR_TYPE.equals(widgetType)) {
                String belong = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.BELONG_TAG);
                String role = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.ROLE_TAG);

                widgetBaseComp = BDuctAreaCalculator.make(srcComponent, name, label, tabInPage, help, inline, belong, role, readOnly, null, bacnetObjectId);
                BComponent tempSelectorDetails = new BComponent();
                for(Property prop : selector.getDynamicPropertiesArray()) {
                	tempSelectorDetails.add(prop.getName(), selector.get(prop), Flags.METADATA);
                }
                ((BDuctAreaCalculator) widgetBaseComp).setAssocaitedSelector(tempSelectorDetails);
                if(widgetBaseComp != null) {
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, role.equals("area") ? grpComponent : null);
                }
            }
            else {
                String minValueStr = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.MIN_TAG);
                String maxValueStr = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.MAX_TAG);

                Double minValue = minValueStr == null ? null : Double.valueOf(minValueStr);
                Double maxValue = maxValueStr == null ? null : Double.valueOf(maxValueStr);
                BUnit unit = ((BIHonWizardNumericPoint) srcComponent).getUnit();

                double step = 1;
                int precision = 0;
                if(null != unit) {
					step = HoneywellConfigurableDeviceUtil.getStep(unit, selector);
					precision = HoneywellConfigurableDeviceUtil.getPrecision(unit, step);
				}

                if(SINGLE_SLIDER.equals(widgetType)) {
                    String color = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.COLOR_TAG);
                    color = com.honeywell.applicationhandler.validation.Constants.getColorCodeForColor(color);
            		String unitGroup = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG) != null
            				? HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG)
            				: "";
                    widgetBaseComp = BSingleSlider.make(srcComponent, name, label, color, tabInPage, help, minValue, maxValue, step, precision, inline, readOnly, bacnetObjectId, unitGroup);
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
                }
                else if(RANGE_SLIDER.equals(widgetType)) {
                    String color = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.COLOR_TAG);
                    color = com.honeywell.applicationhandler.validation.Constants.getColorCodeForColor(color);
                    String belong = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.BELONG_TAG);
                    String role = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.ROLE_TAG);

                    Double deadbandValue = null;

                    try {
                        String deadbandValueStr = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.DEADBAND_TAG);
                        deadbandValue = deadbandValueStr == null || deadbandValueStr.isEmpty() ? 0d : Double.valueOf(deadbandValueStr);
                    }
                    catch (Exception ex) {
                        String msg = lex.getText("HonWizGlobalStoreGenerator.tagvalue.invalid", "deadband", srcComponent.getSlotPath().toString());
                        HoneywellDeviceWizardLogger.severe(msg);
                        throw new GlobalStoreGenerationExcpetion(msg);
                    }


            		String unitGroup = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG) != null
            				? HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG)
            				: "";
                    widgetBaseComp = BRangeSlider.make(srcComponent, name, label, color, tabInPage, help, minValue, maxValue, step, precision, deadbandValue, inline, readOnly, bacnetObjectId, belong, role, unitGroup);
                    if(widgetBaseComp != null) {
                        addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
                    }
                }
                else if(NUMBER_INPUT.equals(widgetType)) {
            		String unitGroup = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG) != null
            				? HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.UNITGRP_TAG)
            				: "";
                    widgetBaseComp = BNumberInput.make(srcComponent, name, label, tabInPage, help, minValue, maxValue, step, precision, inline, readOnly, bacnetObjectId, unitGroup);
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
                }
                else {
                    String msg = lex.getText("HonWizGlobalStoreGenerator.generate.failed", widgetType, srcComponent.getSlotPath());
                    HoneywellDeviceWizardLogger.severe(msg);
                    throw new GlobalStoreGenerationExcpetion(msg);
                }
            }
        }else if(srcComponent instanceof BIHonWizardBooleanPoint) {
            String widgetType = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.WGTTYPE_TAG);
            String tabInPage = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.TAB_IN_PAGE_TAG);
            BBacnetObjectIdentifier bacnetObjectId = ((BIHonWizardBooleanPoint) srcComponent).getBacnetObjectId();
            if(widgetType.equals(SWITCH_BUTTON)) {
                widgetBaseComp = BSwitchButton.make(srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }else if(widgetType.equals(RADIO_BUTTON_GROUP)) {
                widgetBaseComp = BRadioButton.make((BIHonWizardBooleanPoint)srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(widgetType.equals(SELECT_BUTTON)) {
                widgetBaseComp = BSelectButton.make((BIHonWizardBooleanPoint)srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(widgetType.equals(SELECT_WIDGET)) {
                BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);
                widgetBaseComp = BSelectWidget.make((BIHonWizardBooleanPoint)srcComponent, name, label, tabInPage, help, inline, readOnly, tagRange, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(widgetType.equals(DROPDOWN)) {
                widgetBaseComp = BDropdown.make((BIHonWizardBooleanPoint)srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.generate.failed", widgetType, srcComponent.getSlotPath());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        } else if(srcComponent instanceof BIHonWizardEnumPoint) {
            String widgetType = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.WGTTYPE_TAG);
            String tabInPage = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.TAB_IN_PAGE_TAG);
            BBacnetObjectIdentifier bacnetObjectId = ((BIHonWizardEnumPoint) srcComponent).getBacnetObjectId();
            if(widgetType.equals(DROPDOWN)) {
                widgetBaseComp = BDropdown.make((BIHonWizardEnumPoint) srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            } else if(widgetType.equals(RADIO_BUTTON_GROUP)) {
                widgetBaseComp = BRadioButton.make((BIHonWizardEnumPoint) srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(widgetType.equals(SELECT_BUTTON)) {
                widgetBaseComp = BSelectButton.make((BIHonWizardEnumPoint) srcComponent, name, label, tabInPage, help, inline, readOnly, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(widgetType.equals(SELECT_WIDGET)) {
                BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);
                widgetBaseComp = BSelectWidget.make((BIHonWizardEnumPoint) srcComponent, name, label, tabInPage, help, inline, readOnly, tagRange, bacnetObjectId);
                addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, grpComponent);
            }
            else if(DUCT_AREA_CALCULATOR_TYPE.equals(widgetType)) {
                String belong = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.BELONG_TAG);
                String role = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.ROLE_TAG);
                BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag(selector);

                widgetBaseComp = BDuctAreaCalculator.make(srcComponent, name, label, tabInPage, help, inline, belong, role, readOnly, tagRange, bacnetObjectId);
                BComponent tempSelectorDetails = new BComponent();
                for(Property prop : selector.getDynamicPropertiesArray()) {
                	tempSelectorDetails.add(prop.getName(), selector.get(prop), Flags.METADATA);
                }
                ((BDuctAreaCalculator) widgetBaseComp).setAssocaitedSelector(tempSelectorDetails);
                if(widgetBaseComp != null) {
                    addWidgetComponentToPageComponent(generatedTargetComponent, name, widgetBaseComp, role.equals("area") ? grpComponent : null);
                }
            }
            else {
                String msg = lex.getText("HonWizGlobalStoreGenerator.generate.failed", widgetType, srcComponent.getSlotPath());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
        } else if(srcComponent instanceof BIHonWizardEnumSchedule) {
            String widgetType = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.WGTTYPE_TAG);
            if(!widgetType.equals(SCHEDULE)) {
                String msg = lex.getText("HonWizGlobalStoreGenerator.generate.failed", widgetType, srcComponent.getSlotPath());
                HoneywellDeviceWizardLogger.severe(msg);
                throw new GlobalStoreGenerationExcpetion(msg);
            }
            if (device.isScheduleToBeConfigured()) {
                BEnumSchedule schedule = device.getConfiguredEnumSchedule(srcComponent.getName());
                widgetBaseComp = new BSchedulePageWidget();
                ((BSchedulePageWidget) widgetBaseComp).setBacnetObjectId(((BIHonWizardEnumSchedule) srcComponent).getBacnetObjectId());
                ((BSchedulePageWidget) widgetBaseComp).setEnumSchedule((BEnumSchedule) schedule.newCopy(true));
				if (generatedTargetComponent.get(generatedTargetComponent.getName()) != null) {
					String msg = lex.getText("HonWizGlobalStoreGenerator.multi.schedule", generatedTargetComponent.getName());
					HoneywellDeviceWizardLogger.severe(msg);
					throw new GlobalStoreGenerationExcpetion(msg);
				} else {
					generatedTargetComponent.add(generatedTargetComponent.getName(), widgetBaseComp);
				}
            }
        }
        else {
            HoneywellDeviceWizardLogger.info(lex.getText("HonWizGlobalStoreGenerator.point.notsupported", srcComponent.getSlotPath().toString()));
        }

		if (null != widgetBaseComp && null != widgetBaseComp.getSlotPathOrd()) {
			((BIHonWizardPoint) srcComponent).setWizardComponentOrd(widgetBaseComp.getSlotPathOrd());
		}
    }

    private void generateTerminalAssignment(List<String> pageNames, BHonWizardGlobalStore globalStore){
        if(device.isSupportTerminalAssignment()){
            globalStore.add(SlotPath.escape(Const.TERMINAL_ASSIGNMENT_PAGE), device.getTerminalAssignments(null));
            pageNames.add(Const.TERMINAL_ASSIGNMENT_PAGE);
        }
    }

    private BHonWizardGlobalStore createNewGlobalStore() {
        BHonWizardGlobalStore newGlobalStore = new BHonWizardGlobalStore();

        // Copy all tags from the existing global store to the new global store
        device.getGlobalStore().tags().forEach(tag -> {
            if(tag.getId().getDictionary().equals(Const.HON_WIZARD_TAG_NAMESPACE)) {
                newGlobalStore.tags().set(tag);
            }
        });

        return newGlobalStore;
    }

    private void checkAndAddPageOrder(List<String> pageNames, BHonWizardGlobalStore newGlobalStore) {

        List<String> pageNamesInTag = new ArrayList<>();
		for (int i = 0; i < BHonWizardTag.WIZ_PAGE_ORDER.size(); i++) {
			Id id = BHonWizardTag.WIZ_PAGE_ORDER.get(i);
			Optional<BIDataValue> tagValueOptional = newGlobalStore.tags().get(id);
			if (tagValueOptional.isPresent()) {
				String pageName = tagValueOptional.get().toString();
				if (!pageNames.contains(pageName)) {
					newGlobalStore.tags().removeAll(id);
				} else {
					pageNamesInTag.add(pageName);
					pageNames.remove(pageName);
				}
			}
		}

		for (int i = 0; i < BHonWizardTag.WIZ_PAGE_ORDER.size(); i++) {
			Id id = BHonWizardTag.WIZ_PAGE_ORDER.get(i);
			if (i < pageNamesInTag.size()) {
				newGlobalStore.tags().set(id, BString.make(pageNamesInTag.get(i)));
			} else {
				int index = i - pageNamesInTag.size();
				if (index < pageNames.size()) {
					newGlobalStore.tags().set(id, BString.make(pageNames.get(index)));
				} else {
					newGlobalStore.tags().removeAll(id);
				}
			}
		}
    }

    private void checkAndCreateWidgetCompAndSlotSelectorsForDuctAreaCalculator(BComponent pageComponent) {
        List<BDuctAreaCalculator> allCalculatorComponents = Arrays.asList(pageComponent.getChildren(BDuctAreaCalculator.class));
        if(allCalculatorComponents.isEmpty()) {
            return;
        }

        Map<String, List<BDuctAreaCalculator>> groupedByBelong = allCalculatorComponents.stream().collect(Collectors.groupingBy(BDuctAreaCalculator::getBelong));
        String metaDataSlotPath = device.getMetaDataComp().getSlotPath().toString();
        groupedByBelong.keySet().forEach(belong -> {
            List<BDuctAreaCalculator> calculatorComponents = groupedByBelong.get(belong);
            Optional<BDuctAreaCalculator> areaComponentOptional = calculatorComponents.stream().filter(c -> c.getRole().equals(BDuctAreaCalculator.getAreaRole())).findFirst();
            if(areaComponentOptional.isPresent()) {
                BDuctAreaCalculator areaComponent = areaComponentOptional.get();
                // append the rest components to pageComponent.
                BDuctAreaCalculator.ROLES.forEach(role -> {
                    boolean found = calculatorComponents.stream().anyMatch(c -> c.getRole().equals(role));
                    if(!found) {
                        BDuctAreaCalculator calculator = BDuctAreaCalculator.makeDefault(role, belong, areaComponent.getTabInPage(), areaComponent.getHelp());
                    	HoneywellConfigurableDeviceUtil.createDuctareacalcSlotSelectorAndUpdateCopy(device, metaDataSlotPath, areaComponent, role, calculator);
                        pageComponent.add(calculator.getItemName(), calculator);
                    }
                });
            }
            else {
                // remove all other components from page component
                calculatorComponents.forEach(c -> pageComponent.remove(c));
            }

        });
    }

    private void collectSlotPath(String key, Map<String,List<SlotPath>> slotPathListMap, BComponent component) {
        collectSlotPath(key, slotPathListMap, component.getSlotPath());
    }

    private void collectSlotPath(String key, Map<String,List<SlotPath>> slotPathListMap, SlotPath sp) {
        if(slotPathListMap.get(key) != null) {
            List<SlotPath> slotPathList = slotPathListMap.get(key);
            slotPathList.add(sp);
        }else {
            List<SlotPath> slotPathList = new ArrayList<>();
            slotPathList.add(sp);
            slotPathListMap.put(key, slotPathList);
        }
    }

    private Map<String, Map<String, List<SlotPath>>> collectSlotPathByTabsGroups(List<SlotPath> pageSlotPathList) {
        Map<String, Map<String, List<SlotPath>>> tabNameToGrpNameToSlotPathList = new LinkedHashMap<>();

        for(int i=0;i<pageSlotPathList.size();i++) {
            BOrd ord = BOrd.make(pageSlotPathList.get(i).toString());
            BComponent cp = (BComponent)ord.get((BComponent) device);

            String tabInPage = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(cp, BHonWizardTag.TAB_IN_PAGE_TAG);
            if(tabInPage == null) {
                tabInPage = "";
            }

            Map<String, List<SlotPath>> grpNameToSlotPathList = tabNameToGrpNameToSlotPathList.get(tabInPage);
            if(grpNameToSlotPathList == null) {
                grpNameToSlotPathList = new LinkedHashMap<>();
                tabNameToGrpNameToSlotPathList.put(tabInPage, grpNameToSlotPathList);
            }

            String group = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(cp, BHonWizardTag.GRP_TAG);
            if(group == null) {
                group = "";
            }

            List<SlotPath> slotPathList = grpNameToSlotPathList.get(group);
            if(slotPathList == null) {
                slotPathList = new ArrayList<>();
                grpNameToSlotPathList.put(group, slotPathList);
            }

            slotPathList.add(pageSlotPathList.get(i));
        }

        return tabNameToGrpNameToSlotPathList;
    }

    private void addWidgetComponentToPageComponent(BComponent pageComponent, String name, BWidgetComponentBase widgetBaseComp, BComponent grpComponent) {
        name = SlotPath.unescape(name);
        pageComponent.add(name + "?", widgetBaseComp);
        if(grpComponent != null) {
            String existingItems = grpComponent.get("itemsInGroup").toString();
            if(existingItems.isEmpty()) {
                grpComponent.set("itemsInGroup", BString.make(widgetBaseComp.getName()));
            } else {
                grpComponent.set("itemsInGroup", BString.make(existingItems + "," + widgetBaseComp.getName()));
            }
        }
    }

    private static final String PATH_LIMITER = "/";
}
