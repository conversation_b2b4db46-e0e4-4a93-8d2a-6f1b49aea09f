package com.honeywell.applicationhandler.device;

import java.util.HashMap;
import java.util.Map;

import javax.baja.nre.annotations.NiagaraAction;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Action;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BStation;
import javax.baja.sys.BString;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.applicationhandler.jobs.BHonWizardRuleParserJob;
import com.honeywell.applicationhandler.utils.BConstraintRuleShell;
import com.honeywell.applicationhandler.utils.BHonWizardRuleShell;
import com.honeywell.applicationhandler.utils.BTerminalAssignmentValueRuleShell;
import com.honeywell.applicationhandler.utils.BValueRuleShell;
import com.honeywell.applicationhandler.utils.BVisibilityRuleShell;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;

@NiagaraType
@NiagaraAction(name="generateRuleConfig", flags = Flags.HIDDEN)
@NiagaraAction(name="validateRuleConfig")
@NiagaraAction(name="addValueRule")
@NiagaraAction(name="addVisibilityRule")
@NiagaraAction(name="addTerminalAssignmentValueRule")
@NiagaraAction(name="addConstraintRule")
@NiagaraAction(name="clearAllRules")
public class BHonWizardRuleStore extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.device.BHonWizardRuleStore(3105830371)1.0$ @*/
/* Generated Fri Jun 27 16:35:40 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Action "generateRuleConfig"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code generateRuleConfig} action.
   * @see #generateRuleConfig()
   */
  public static final Action generateRuleConfig = newAction(Flags.HIDDEN, null);
  
  /**
   * Invoke the {@code generateRuleConfig} action.
   * @see #generateRuleConfig
   */
  public void generateRuleConfig() { invoke(generateRuleConfig, null, null); }

////////////////////////////////////////////////////////////////
// Action "validateRuleConfig"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code validateRuleConfig} action.
   * @see #validateRuleConfig()
   */
  public static final Action validateRuleConfig = newAction(0, null);
  
  /**
   * Invoke the {@code validateRuleConfig} action.
   * @see #validateRuleConfig
   */
  public void validateRuleConfig() { invoke(validateRuleConfig, null, null); }

////////////////////////////////////////////////////////////////
// Action "addValueRule"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code addValueRule} action.
   * @see #addValueRule()
   */
  public static final Action addValueRule = newAction(0, null);
  
  /**
   * Invoke the {@code addValueRule} action.
   * @see #addValueRule
   */
  public void addValueRule() { invoke(addValueRule, null, null); }

////////////////////////////////////////////////////////////////
// Action "addVisibilityRule"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code addVisibilityRule} action.
   * @see #addVisibilityRule()
   */
  public static final Action addVisibilityRule = newAction(0, null);
  
  /**
   * Invoke the {@code addVisibilityRule} action.
   * @see #addVisibilityRule
   */
  public void addVisibilityRule() { invoke(addVisibilityRule, null, null); }

////////////////////////////////////////////////////////////////
// Action "addTerminalAssignmentValueRule"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code addTerminalAssignmentValueRule} action.
   * @see #addTerminalAssignmentValueRule()
   */
  public static final Action addTerminalAssignmentValueRule = newAction(0, null);
  
  /**
   * Invoke the {@code addTerminalAssignmentValueRule} action.
   * @see #addTerminalAssignmentValueRule
   */
  public void addTerminalAssignmentValueRule() { invoke(addTerminalAssignmentValueRule, null, null); }

////////////////////////////////////////////////////////////////
// Action "addConstraintRule"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code addConstraintRule} action.
   * @see #addConstraintRule()
   */
  public static final Action addConstraintRule = newAction(0, null);
  
  /**
   * Invoke the {@code addConstraintRule} action.
   * @see #addConstraintRule
   */
  public void addConstraintRule() { invoke(addConstraintRule, null, null); }

////////////////////////////////////////////////////////////////
// Action "clearAllRules"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code clearAllRules} action.
   * @see #clearAllRules()
   */
  public static final Action clearAllRules = newAction(0, null);
  
  /**
   * Invoke the {@code clearAllRules} action.
   * @see #clearAllRules
   */
  public void clearAllRules() { invoke(clearAllRules, null, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardRuleStore.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

  @Override
	public boolean isParentLegal(BComponent parent) {
		return (parent instanceof BIHoneywellConfigurableDevice);
	}
  
	@Override
	public boolean isChildLegal(BComponent child) {
		return (child instanceof BHonWizardRuleShell);
	}
	
	@Override
	public void added(Property property, Context context) {
		super.added(property, context);
		if (this.get(property) instanceof BVisibilityRuleShell) {
			BVisibilityRuleShell visibilityRuleShell = (BVisibilityRuleShell) this.get(property);
			BString visibilityRule = BString.make(VISIBILITY_RULE);
			String visibilityRuleShellName = visibilityRuleShell.getName();
			this.remove(property);
			this.add(visibilityRuleShellName, visibilityRule, 0,
					BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);

		} else if (this.get(property) instanceof BValueRuleShell) {
			BValueRuleShell valueRuleShell = (BValueRuleShell) this.get(property);
			BString valueRule = BString.make(VALUE_RULE);
			String valueRuleShellName = valueRuleShell.getName();
			this.remove(property);
			this.add(valueRuleShellName, valueRule, 0, BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)),
					null);
		} else if (this.get(property) instanceof BTerminalAssignmentValueRuleShell) {
			BTerminalAssignmentValueRuleShell terminalValueRuleShell = (BTerminalAssignmentValueRuleShell) this.get(property);
			BString valueRule = BString.make(TERMINAL_ASSIGNMENT_VALUE_RULE);
			String valueRuleShellName = terminalValueRuleShell.getName();
			this.remove(property);
			this.add(valueRuleShellName, valueRule, 0, BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)),
					null);
		} else if(this.get(property) instanceof BConstraintRuleShell) {
			BConstraintRuleShell constraintRuleShell = (BConstraintRuleShell) this.get(property);
			BString constraintRule = BString.make(CONSTRAINT_RULE);
			String constraintRuleShellName = constraintRuleShell.getName();
			this.remove(property);
			this.add(constraintRuleShellName, constraintRule, 0,
					BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
		}
	}
  
	public void doGenerateRuleConfig() {
		BHonWizardRuleParserJob job = new BHonWizardRuleParserJob((BIHoneywellConfigurableDevice) this.getParent(), this, false);
		job.submit(null);
	}

	public void doValidateRuleConfig() {
		BHonWizardRuleParserJob job = new BHonWizardRuleParserJob((BIHoneywellConfigurableDevice) this.getParent(), this, true);
		job.submit(null);
	}


	public void doAddValueRule() {
		BString valueRule = BString.make(VALUE_RULE);
		this.add(VALUE_RULE_NAME, valueRule, 0, BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}

	public void doAddVisibilityRule() {
		BString visibilityRule = BString.make(VISIBILITY_RULE);
		this.add(VISIBILITY_RULE_NAME, visibilityRule, 0,
				BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}

	public void doAddTerminalAssignmentValueRule() {
		BString terminalAssignmentValueRule = BString.make(TERMINAL_ASSIGNMENT_VALUE_RULE);
		this.add(TERMINAL_ASSIGNMENT_VALUE_RULE_NAME, terminalAssignmentValueRule, 0,
				BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}
	
	public void doAddConstraintRule() {
		BString constraintRule = BString.make(CONSTRAINT_RULE);
		this.add(CONSTRAINT_RULE_NAME, constraintRule, 0,
				BFacets.make(BFacets.make(BFacets.MULTI_LINE, true), BFacets.make(BFacets.FIELD_WIDTH, 135)), null);
	}
	
	public void doClearAllRules() {
        for (Property rule : this.getDynamicPropertiesArray()) {
        	if(this.get(rule) instanceof BString) {
        		this.remove(rule);
        	}
        }
	}
	
    public Map<String, String> getAllRules() {
        Map<String, String> rules = new HashMap<>();
        for (Property rule : this.getDynamicPropertiesArray()) {
        	if(this.get(rule) instanceof BString) {
        		rules.put(rule.getName(), ((BString) this.get(rule)).getString());
        	}
        }
        return rules;
    }

	@Override
	public void changed(Property property, Context context) {
		super.changed(property, context);
		if (null != selectorDevice) {
			selectorDevice.honWizardSelectorChanged(true, context);
		}
	}

	@Override
	public void started() throws Exception {
		super.started();
		selectorDevice = HoneywellConfigurableDeviceUtil.getDevice(this.getParent());
	}


    
	private static final String VISIBILITY_RULE 		= "ADD VISIBILITY RULE TO: <PrimaryTab>: \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<action> \n\tELSE \n\t\t<action>";
	private static final String VALUE_RULE 				= "ADD VALUE RULE TO: <PrimaryTab>: \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<action> \n\tELSE \n\t\t<action>";
	private static final String VISIBILITY_RULE_NAME 	= "VisibilityRule?";
	private static final String VALUE_RULE_NAME 		= "ValueRule?";
	private static final String TERMINAL_ASSIGNMENT_VALUE_RULE 		= "ADD TERMINAL_ASSIGNMENT_VALUE RULE TO: Terminal Assignment: \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<action> \n\tELSE \n\t\t<action>";
	private static final String TERMINAL_ASSIGNMENT_VALUE_RULE_NAME 	= "TerminalAssignmentValueRule?";

	private static final String CONSTRAINT_RULE 		= "ADD CONSTRAINT RULE TO: <PrimaryTab>: WITH DEADBAND AS: <deadband object> \n\tIF \n\t\t<condition> \n\tTHEN \n\t\t<limit action>";
	private static final String CONSTRAINT_RULE_NAME 	= "ConstraintRule?";
	BIHoneywellConfigurableDevice selectorDevice = null;

}
