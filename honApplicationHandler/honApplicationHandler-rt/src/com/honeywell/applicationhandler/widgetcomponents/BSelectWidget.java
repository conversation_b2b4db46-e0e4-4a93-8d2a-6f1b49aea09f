/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardEnumPoint;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0", type = "int")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "defaultValue", defaultValue = "0", type = "int")
@NiagaraProperty (name = "componentType", defaultValue = "SelectWidget", type = "String", flags = Flags.READONLY)
@NiagaraProperty (name = "options", defaultValue = "new BComponent()", type = "BComponent")
@NiagaraType
public class BSelectWidget extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BSelectWidget(3204183479)1.0$ @*/
/* Generated Sun Feb 09 13:35:15 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public int getValue() { return getInt(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(int v) { setInt(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "defaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code defaultValue} property.
   * @see #getDefaultValue
   * @see #setDefaultValue
   */
  public static final Property defaultValue = newProperty(0, 0, null);
  
  /**
   * Get the {@code defaultValue} property.
   * @see #defaultValue
   */
  public int getDefaultValue() { return getInt(defaultValue); }
  
  /**
   * Set the {@code defaultValue} property.
   * @see #defaultValue
   */
  public void setDefaultValue(int v) { setInt(defaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, "SelectWidget", null);
  
  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }
  
  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Property "options"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code options} property.
   * @see #getOptions
   * @see #setOptions
   */
  public static final Property options = newProperty(0, new BComponent(), null);
  
  /**
   * Get the {@code options} property.
   * @see #options
   */
  public BComponent getOptions() { return (BComponent)get(options); }
  
  /**
   * Set the {@code options} property.
   * @see #options
   */
  public void setOptions(BComponent v) { set(options, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSelectWidget.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BSelectWidget make(BIHonWizardBooleanPoint srcComponent,
                                     String name,
                                     String label,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly,
                                     BEnumRange tagRange,
                                     BBacnetObjectIdentifier bacnetObjectId
    ) {
        int value = srcComponent.getValue() ? 1 : 0;
        int defaultValue = srcComponent.getDefault() ? 1 : 0;
        //BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag((BComponent) srcComponent);
        BSelectWidget result = make(name, value, defaultValue, label, srcComponent.getEnumRange(), tagRange, tabInPage, help, inline, readOnly);
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");

        return result;
    }

    public static BSelectWidget make(BIHonWizardEnumPoint srcComponent,
                                     String name,
                                     String label,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly,
                                     BEnumRange tagRange,
                                     BBacnetObjectIdentifier bacnetObjectId
    ) {
        int value = srcComponent.getValue().getOrdinal();
        int defaultValue = srcComponent.getDefault().getOrdinal();
        //BEnumRange tagRange = HoneywellConfigurableDeviceUtil.getOptionsFromTag((BComponent) srcComponent);
        BSelectWidget result = make(name, value, defaultValue, label, srcComponent.getEnumRange(), tagRange, tabInPage, help, inline, readOnly);
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");

        return result;
    }

    public static BSelectWidget make(String name,
                                     int value,
                                     int defaultValue,
                                     String label,
                                     BEnumRange range,
                                     BEnumRange tagRange,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly,
                                     BOrd commonSlot
    ) {
        BSelectWidget result = make(name, value, defaultValue, label, range, tagRange, tabInPage, help, inline, readOnly);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");

        return result;
    }

    public static BSelectWidget make(String name,
                                     int value,
                                     int defaultValue,
                                      String label,
                                      BEnumRange range,
                                      BEnumRange tagRange,
                                      String tabInPage,
                                      String help,
                                      String inline,
                                      Boolean readOnly
    ) {
        BSelectWidget selectWidget = new BSelectWidget();
        selectWidget.setItemName(name);
        selectWidget.setLabel(label);
        selectWidget.setValue(value);
        selectWidget.setDefaultValue(defaultValue);
        selectWidget.setVisible(true);
        selectWidget.setTabInPage(tabInPage == null ? "" : tabInPage);
        selectWidget.setHelp(help == null ? "" : help);
        selectWidget.setInline(inline);
        selectWidget.setReadOnly(readOnly);

        selectWidget.makeOptions(tagRange, range);

        return selectWidget;
    }

    @Override
    public void makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        if (targetEnumRange == null) {
            return;
        }
        BComponent options = new BComponent();
        for (int i : targetEnumRange.getOrdinals()) {
            String tag = targetEnumRange.getDisplayTag(i, null);
            BOption option = new BOption();
            option.setKey(i);
            option.setText(tag);
            option.setValue(i);
            if(tagEnumRange != null && tagEnumRange.isOrdinal(i)) {
                String image = SlotPath.unescape(tagEnumRange.getTag(i));
                option.setImage(image);
            }
            options.add("option?", option);
        }
        setOptions(options);
    }

    @Override
    public void setValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BSelectWidget (" + getLabel() + "): " + value);
        }
    }
    
    @Override
    public void setDefaultValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setDefaultValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid default value for BSelectWidget (" + getLabel() + "): " + value);
        }
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(Integer.toString(getValue()));
	}
    
}
