/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;


import com.honeywell.applicationhandler.common.Constants;
import com.honeywell.applicationhandler.enums.BTimezoneEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> - Shyamsundhar Madhusudhan
 * @since Jun 10, 2025
 */
@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "BTimezoneEnum.DEFAULT", type = "BTimezoneEnum")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "defaultValue", defaultValue = "BTimezoneEnum.DEFAULT", type = "BTimezoneEnum")
@NiagaraProperty (name = "componentType", defaultValue = "Constants.TIMEZONE_TYPE", type = "String", flags = Flags.READONLY)
@NiagaraProperty (name = "options", defaultValue = "new BComponent()", type = "BComponent")

 @NiagaraType
public class BTimeZone extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BTimeZone(1603656189)1.0$ @*/
/* Generated Fri Jul 04 17:47:31 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, BTimezoneEnum.DEFAULT, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public BTimezoneEnum getValue() { return (BTimezoneEnum)get(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(BTimezoneEnum v) { set(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "defaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code defaultValue} property.
   * @see #getDefaultValue
   * @see #setDefaultValue
   */
  public static final Property defaultValue = newProperty(0, BTimezoneEnum.DEFAULT, null);
  
  /**
   * Get the {@code defaultValue} property.
   * @see #defaultValue
   */
  public BTimezoneEnum getDefaultValue() { return (BTimezoneEnum)get(defaultValue); }
  
  /**
   * Set the {@code defaultValue} property.
   * @see #defaultValue
   */
  public void setDefaultValue(BTimezoneEnum v) { set(defaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, Constants.TIMEZONE_TYPE, null);
  
  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }
  
  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Property "options"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code options} property.
   * @see #getOptions
   * @see #setOptions
   */
  public static final Property options = newProperty(0, new BComponent(), null);
  
  /**
   * Get the {@code options} property.
   * @see #options
   */
  public BComponent getOptions() { return (BComponent)get(options); }
  
  /**
   * Set the {@code options} property.
   * @see #options
   */
  public void setOptions(BComponent v) { set(options, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTimeZone.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BTimeZone make(String name,
    							 BTimezoneEnum value,
                                 String label,
                                 String tabInPage,
                                 String help,
                                 String inline,
                                 Boolean readOnly,
                                 BOrd commonSlot
    ) {
        BTimeZone timeZone = new BTimeZone();
        timeZone.setItemName(name);
        timeZone.setLabel(label);
        timeZone.setValue(value);
        timeZone.setVisible(true);
        timeZone.setTabInPage(tabInPage == null ? "" : tabInPage);
        timeZone.setHelp(help == null ? "" : help);
        timeZone.setInline(inline);
        timeZone.setReadOnly(readOnly);
        BComponent options = new BComponent();
        BEnumRange enumRange = BTimezoneEnum.DEFAULT.getRange();
        
        for (int i : enumRange.getOrdinals()) {
            String tag = enumRange.getDisplayTag(i, null);
            BOption option = new BOption();
            option.setKey(i);
            option.setText(tag);
            option.setValue(i);
            options.add("option?", option);
        }

        timeZone.setOptions(options);

        timeZone.hideSlot("bacnetObjectId");
        timeZone.setCommonSlot(commonSlot);

        return timeZone;
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(String.valueOf(getValue().getOrdinal()));
	}

	@Override
	public void setValue(String value) {
		setValue(BTimezoneEnum.make(Integer.valueOf(value)));
		
	}

	@Override
	public void setDefaultValue(String value) {
		setDefaultValue(BTimezoneEnum.make(Integer.valueOf(value)));		
	}
    
}