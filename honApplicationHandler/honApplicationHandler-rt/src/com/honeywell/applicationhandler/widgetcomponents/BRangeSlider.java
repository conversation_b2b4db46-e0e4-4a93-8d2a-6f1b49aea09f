/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import java.util.logging.Logger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0.0", type = "double")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "componentType", defaultValue = "RangeSlider", type = "String", flags = Flags.READONLY)
@NiagaraProperty (name = "color", defaultValue = "", type = "String")
@NiagaraProperty (name = "deadband", defaultValue = "0.0", type = "double")
@NiagaraProperty(name = "highPrecisionDeadband", defaultValue = "", type = "String")
@NiagaraProperty (name = "belong", defaultValue = "", type = "String")
@NiagaraProperty (name = "role", defaultValue = "", type = "String")
@NiagaraType
public class BRangeSlider extends BDynamicWidgetUnitSupportComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BRangeSlider(3994506751)1.0$ @*/
/* Generated Wed Mar 19 14:16:11 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public double getValue() { return getDouble(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(double v) { setDouble(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, "RangeSlider", null);
  
  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }
  
  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Property "color"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code color} property.
   * @see #getColor
   * @see #setColor
   */
  public static final Property color = newProperty(0, "", null);
  
  /**
   * Get the {@code color} property.
   * @see #color
   */
  public String getColor() { return getString(color); }
  
  /**
   * Set the {@code color} property.
   * @see #color
   */
  public void setColor(String v) { setString(color, v, null); }

////////////////////////////////////////////////////////////////
// Property "deadband"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code deadband} property.
   * @see #getDeadband
   * @see #setDeadband
   */
  public static final Property deadband = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code deadband} property.
   * @see #deadband
   */
  public double getDeadband() { return getDouble(deadband); }
  
  /**
   * Set the {@code deadband} property.
   * @see #deadband
   */
  public void setDeadband(double v) { setDouble(deadband, v, null); }

////////////////////////////////////////////////////////////////
// Property "highPrecisionDeadband"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code highPrecisionDeadband} property.
   * @see #getHighPrecisionDeadband
   * @see #setHighPrecisionDeadband
   */
  public static final Property highPrecisionDeadband = newProperty(0, "", null);
  
  /**
   * Get the {@code highPrecisionDeadband} property.
   * @see #highPrecisionDeadband
   */
  public String getHighPrecisionDeadband() { return getString(highPrecisionDeadband); }
  
  /**
   * Set the {@code highPrecisionDeadband} property.
   * @see #highPrecisionDeadband
   */
  public void setHighPrecisionDeadband(String v) { setString(highPrecisionDeadband, v, null); }

////////////////////////////////////////////////////////////////
// Property "belong"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code belong} property.
   * @see #getBelong
   * @see #setBelong
   */
  public static final Property belong = newProperty(0, "", null);
  
  /**
   * Get the {@code belong} property.
   * @see #belong
   */
  public String getBelong() { return getString(belong); }
  
  /**
   * Set the {@code belong} property.
   * @see #belong
   */
  public void setBelong(String v) { setString(belong, v, null); }

////////////////////////////////////////////////////////////////
// Property "role"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code role} property.
   * @see #getRole
   * @see #setRole
   */
  public static final Property role = newProperty(0, "", null);
  
  /**
   * Get the {@code role} property.
   * @see #role
   */
  public String getRole() { return getString(role); }
  
  /**
   * Set the {@code role} property.
   * @see #role
   */
  public void setRole(String v) { setString(role, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BRangeSlider.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BRangeSlider make(BComponent srcComponent,
                                    String name,
                                    String label,
                                    String color,
                                    String tabInPage,
                                    String help,
                                    double minValue,
                                    double maxValue,
                                    double step,
                                    int precision,
                                    double deadband,
                                    String inline,
                                    Boolean readOnly,
                                    BBacnetObjectIdentifier bacnetObjectId,
                                    String belong,
                                    String role,
                                    String unitGroup
    ) {
        //TODO: value will always be double??
        double value = ((BIHonWizardNumericPoint) srcComponent).getValue();
        double defaultValue = ((BIHonWizardNumericPoint) srcComponent).getDefault();
        BUnit unit = ((BIHonWizardNumericPoint) srcComponent).getUnit();
        BRangeSlider result = make(name, value, defaultValue, unit, step, precision, label, color, tabInPage, help, minValue, maxValue, deadband,
                inline, readOnly, belong, role, String.valueOf(value),String.valueOf(minValue), String.valueOf(maxValue),
                String.valueOf(defaultValue), String.valueOf(deadband), unitGroup);
        if(result == null) {
            return null;
        }

        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");
        return result;
    }

    public static BRangeSlider make(String name,
                                    double value,
                                    double defaultValue,
                                    BUnit unit,
                                    double step,
                                    int precision,
                                    String label,
                                    String color,
                                    String tabInPage,
                                    String help,
                                    double minValue,
                                    double maxValue,
                                    double deadband,
                                    String inline,
                                    Boolean readOnly,
                                    String belong,
                                    String role,
                                    BOrd commonSlot,
                                    String highPrecisionValue,
                                    String highPrecisionMin,
                                    String highPrecisionMax,
                                    String highPrecisionDefaultValue,
                                    String highPrecisionDeadBand,
                                    String unitGroup
    ) {
        BRangeSlider result = make(name, value, defaultValue, unit, step, precision, label, color, tabInPage, help, minValue, maxValue, deadband, inline, readOnly, belong, role,
                highPrecisionValue, highPrecisionMin, highPrecisionMax, highPrecisionDefaultValue, highPrecisionDeadBand, unitGroup);
        if(result == null) {
            return null;
        }

        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");
        return result;
    }

    private static BRangeSlider make(String name,
                                    double value,
                                    double defaultValue,
                                    BUnit unit,
                                    double step,
                                    int precision,
                                    String label,
                                    String color,
                                    String tabInPage,
                                    String help,
                                    double minValue,
                                    double maxValue,
                                    double deadband,
                                    String inline,
                                    Boolean readOnly,
                                    String belong,
                                    String role,
                                    String highPrecisionValue,
                                    String highPrecisionMin,
                                    String highPrecisionMax,
                                    String highPrecisionDefaultValue,
                                    String highPrecisionDeadBand,
                                    String unitGroup
    ) {
        if(belong == null || belong.isEmpty() || role == null || role.isEmpty()) {
            logger.warning("RangeSlider creation issue: Belong or Role is empty for " + name + "(" + label + ")");
            return null;
        }

        BRangeSlider rangeSlider = new BRangeSlider();
        rangeSlider.setItemName(name);
        rangeSlider.setLabel(label);
        rangeSlider.setValue(value);
        rangeSlider.setDefaultValue(defaultValue);
        rangeSlider.setVisible(true);
        rangeSlider.setColor(color != null ? color : "");
        //How to get it? from tags??
        rangeSlider.setMin(minValue);
        //How to get it? from tags??
        rangeSlider.setMax(maxValue);
        rangeSlider.setDeadband(deadband);
        rangeSlider.setTabInPage(tabInPage == null ? "" : tabInPage);
        rangeSlider.setHelp(help == null ? "" : help);
        rangeSlider.setInline(inline);
        rangeSlider.setReadOnly(readOnly);

        rangeSlider.setBelong(belong);
        rangeSlider.setRole(role);
        rangeSlider.setUnit(unit == null || "null".equals(unit.getSymbol()) ? "" : unit.getSymbol());
        if(null != unit) {
        	rangeSlider.setFacets(BRangeSlider.unit, BFacets.make(BFacets.UNITS, unit));
        }
        rangeSlider.setHighPrecisionValue(highPrecisionValue);
        rangeSlider.setHighPrecisionMin(highPrecisionMin);
        rangeSlider.setHighPrecisionMax(highPrecisionMax);
        rangeSlider.setHighPrecisionDefaultValue(highPrecisionDefaultValue);
        rangeSlider.setHighPrecisionDeadband(highPrecisionDeadBand);

        rangeSlider.setPrecision(precision);
        rangeSlider.setStep(step);
        
		// Performed direct enum tag comparison as EnumRange is missing due to
		// current Niagara limitations.
		if (null != unitGroup && !unitGroup.isEmpty()) {
			if (BUnitGroupEnum.measurementType.getDisplayTag(null).equals(unitGroup)) {
				rangeSlider.setUnitGroup(BUnitGroupEnum.measurementType);
			} else if(BUnitGroupEnum.airflowUnit.getDisplayTag(null).equals(unitGroup)) {
				rangeSlider.setUnitGroup(BUnitGroupEnum.airflowUnit);
			} else {
				rangeSlider.setUnitGroup(BUnitGroupEnum.none);
			}
		}
        return rangeSlider;
    }

    @Override
    public void setValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setValue(doubleValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BRangeSlider (" + getLabel() + "): " + value);
        }
    }
    
    @Override
    public void setDefaultValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setDefaultValue(doubleValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid default value for BRangeSlider (" + getLabel() + "): " + value);
        }
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(Double.toString(getValue()));
	}


}
