/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

@NiagaraProperty(name = "key", defaultValue = "1", type = "int")
@NiagaraProperty (name = "text", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "1", type = "int")
@NiagaraProperty(name = "image", defaultValue = "", type = "String")
@NiagaraType
public class BOption extends BComponent {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BOption(1094707196)1.0$ @*/
/* Generated Wed Nov 20 14:03:36 CST 2024 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "key"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code key} property.
   * @see #getKey
   * @see #setKey
   */
  public static final Property key = newProperty(0, 1, null);
  
  /**
   * Get the {@code key} property.
   * @see #key
   */
  public int getKey() { return getInt(key); }
  
  /**
   * Set the {@code key} property.
   * @see #key
   */
  public void setKey(int v) { setInt(key, v, null); }

////////////////////////////////////////////////////////////////
// Property "text"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code text} property.
   * @see #getText
   * @see #setText
   */
  public static final Property text = newProperty(0, "", null);
  
  /**
   * Get the {@code text} property.
   * @see #text
   */
  public String getText() { return getString(text); }
  
  /**
   * Set the {@code text} property.
   * @see #text
   */
  public void setText(String v) { setString(text, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 1, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public int getValue() { return getInt(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(int v) { setInt(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "image"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code image} property.
   * @see #getImage
   * @see #setImage
   */
  public static final Property image = newProperty(0, "", null);
  
  /**
   * Get the {@code image} property.
   * @see #image
   */
  public String getImage() { return getString(image); }
  
  /**
   * Set the {@code image} property.
   * @see #image
   */
  public void setImage(String v) { setString(image, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BOption.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
}
