/*
 *  Copyright (c) 2024 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

 
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.utils.HoneywellConfigurableDeviceUtil;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0", type = "int")
@NiagaraProperty (name = "visible", defaultValue = "true" , type = "boolean")
@NiagaraProperty (name = "defaultValue", defaultValue = "0", type = "int")
@NiagaraProperty (name = "componentType", defaultValue = "CheckboxGroup", type = "String", flags = Flags.READONLY)
@NiagaraProperty (name = "options", defaultValue = "new BComponent()", type = "BComponent")
@NiagaraProperty (name = "saveConvert", defaultValue = "", type = "String")
@NiagaraProperty (name = "loadConvert", defaultValue = "", type = "String")
 @NiagaraType
public class BCheckbox extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BCheckbox(203311161)1.0$ @*/
/* Generated Sun Feb 09 13:35:15 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public int getValue() { return getInt(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(int v) { setInt(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "defaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code defaultValue} property.
   * @see #getDefaultValue
   * @see #setDefaultValue
   */
  public static final Property defaultValue = newProperty(0, 0, null);
  
  /**
   * Get the {@code defaultValue} property.
   * @see #defaultValue
   */
  public int getDefaultValue() { return getInt(defaultValue); }
  
  /**
   * Set the {@code defaultValue} property.
   * @see #defaultValue
   */
  public void setDefaultValue(int v) { setInt(defaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, "CheckboxGroup", null);
  
  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }
  
  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Property "options"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code options} property.
   * @see #getOptions
   * @see #setOptions
   */
  public static final Property options = newProperty(0, new BComponent(), null);
  
  /**
   * Get the {@code options} property.
   * @see #options
   */
  public BComponent getOptions() { return (BComponent)get(options); }
  
  /**
   * Set the {@code options} property.
   * @see #options
   */
  public void setOptions(BComponent v) { set(options, v, null); }

////////////////////////////////////////////////////////////////
// Property "saveConvert"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code saveConvert} property.
   * @see #getSaveConvert
   * @see #setSaveConvert
   */
  public static final Property saveConvert = newProperty(0, "", null);
  
  /**
   * Get the {@code saveConvert} property.
   * @see #saveConvert
   */
  public String getSaveConvert() { return getString(saveConvert); }
  
  /**
   * Set the {@code saveConvert} property.
   * @see #saveConvert
   */
  public void setSaveConvert(String v) { setString(saveConvert, v, null); }

////////////////////////////////////////////////////////////////
// Property "loadConvert"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code loadConvert} property.
   * @see #getLoadConvert
   * @see #setLoadConvert
   */
  public static final Property loadConvert = newProperty(0, "", null);
  
  /**
   * Get the {@code loadConvert} property.
   * @see #loadConvert
   */
  public String getLoadConvert() { return getString(loadConvert); }
  
  /**
   * Set the {@code loadConvert} property.
   * @see #loadConvert
   */
  public void setLoadConvert(String v) { setString(loadConvert, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BCheckbox.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BCheckbox make(BComponent srcComponent,
                                 String name,
                                 String label,
                                 String tabInPage,
                                 String help,
                                 String inline,
                                 Boolean readOnly,
                                 BEnumRange er,
                                 BBacnetObjectIdentifier bacnetObjectId
    ) {
        int value = (int)((BIHonWizardNumericPoint) srcComponent).getValue();
        int defaultValue = (int)((BIHonWizardNumericPoint) srcComponent).getDefault();
        if(er == null) {
            logger.severe("No options found for "+srcComponent.getSlotPath());
            return null;
        }

        BCheckbox result = make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, er);
        if(result == null) {
            return null;
        }
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");

        return result;
    }

    public static BCheckbox make(String name,
                                 int value,
                                 int defaultValue,
                                 String label,
                                 String tabInPage,
                                 String help,
                                 String inline,
                                 Boolean readOnly,
                                 BEnumRange er,
                                 BOrd commonSlot
    ) {
        if(er == null) {
            logger.severe("No options found for "+commonSlot);
            return null;
        }

        BCheckbox result = make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, er);
        if(result == null) {
            return null;
        }
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");

        return result;
    }

    private static BCheckbox make(String name,
                                 int value,
                                 int defaultValue,
                                 String label,
                                 String tabInPage,
                                 String help,
                                 String inline,
                                 Boolean readOnly,
                                 BEnumRange er
    ) {
        BCheckbox checkBox = new BCheckbox();
        checkBox.setItemName(name);
        checkBox.setLabel(label);

        checkBox.setValue(value);
        checkBox.setDefaultValue(defaultValue);
        checkBox.setVisible(true);
        checkBox.setTabInPage(tabInPage == null ? "" : tabInPage);
        checkBox.setHelp(help == null ? "" : help);
        checkBox.setInline(inline);
        checkBox.setReadOnly(readOnly);
        if(er == null) {
            logger.severe("No options found for checkbox group");
            return null;
        }
        checkBox.makeOptions(er, null);
        checkBox.setSaveConvert(HoneywellConfigurableDeviceUtil.getTransferNumberListToBitwiseValueFuncString());

        checkBox.setLoadConvert(HoneywellConfigurableDeviceUtil.getTransferBitwiseValueToNumberListFuncString());

        return checkBox;
    }

    @Override
    public void makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {
        BComponent options = new BComponent();
        if(tagEnumRange != null) {
            for(int i=0;i<tagEnumRange.getOrdinals().length;i++) {
                int ordinal = tagEnumRange.getOrdinals()[i];
                String tag = tagEnumRange.getDisplayTag(ordinal, null);
                BOption option = new BOption();
                option.setKey(i);
                option.setText(tag);
                option.setValue(ordinal);
                options.add("option?", option);
            }
        }
        setOptions(options);
    }

    @Override
    public void setValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BCheckbox (" + getLabel() + "): " + value);
        }
    }
    
    @Override
    public void setDefaultValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setDefaultValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid default value for BCheckbox (" + getLabel() + "): " + value);
        }
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(Integer.toString(getValue()));
	}

}