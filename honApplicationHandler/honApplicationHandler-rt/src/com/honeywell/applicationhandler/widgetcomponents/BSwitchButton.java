/*
 *  Copyright (c) 2024 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part 
 *  may be reproduced or transmitted in any form by any means or for any 
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

 
import com.honeywell.applicationhandler.device.points.BIHonWizardBooleanPoint;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import java.util.logging.Logger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0", type = "int")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "defaultValue", defaultValue = "0", type = "int")
@NiagaraProperty (name = "componentType", defaultValue = "SwitchButton", type = "String", flags = Flags.READONLY)
@NiagaraProperty (name = "saveConvert", defaultValue = "", type = "String")
@NiagaraProperty (name = "loadConvert", defaultValue = "", type = "String")
 @NiagaraType
public class BSwitchButton extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BSwitchButton(1130040447)1.0$ @*/
/* Generated Fri Aug 08 09:42:38 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0, null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public int getValue() { return getInt(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(int v) { setInt(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "defaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code defaultValue} property.
   * @see #getDefaultValue
   * @see #setDefaultValue
   */
  public static final Property defaultValue = newProperty(0, 0, null);
  
  /**
   * Get the {@code defaultValue} property.
   * @see #defaultValue
   */
  public int getDefaultValue() { return getInt(defaultValue); }
  
  /**
   * Set the {@code defaultValue} property.
   * @see #defaultValue
   */
  public void setDefaultValue(int v) { setInt(defaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, "SwitchButton", null);
  
  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }
  
  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Property "saveConvert"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code saveConvert} property.
   * @see #getSaveConvert
   * @see #setSaveConvert
   */
  public static final Property saveConvert = newProperty(0, "", null);
  
  /**
   * Get the {@code saveConvert} property.
   * @see #saveConvert
   */
  public String getSaveConvert() { return getString(saveConvert); }
  
  /**
   * Set the {@code saveConvert} property.
   * @see #saveConvert
   */
  public void setSaveConvert(String v) { setString(saveConvert, v, null); }

////////////////////////////////////////////////////////////////
// Property "loadConvert"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code loadConvert} property.
   * @see #getLoadConvert
   * @see #setLoadConvert
   */
  public static final Property loadConvert = newProperty(0, "", null);
  
  /**
   * Get the {@code loadConvert} property.
   * @see #loadConvert
   */
  public String getLoadConvert() { return getString(loadConvert); }
  
  /**
   * Set the {@code loadConvert} property.
   * @see #loadConvert
   */
  public void setLoadConvert(String v) { setString(loadConvert, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BSwitchButton.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BSwitchButton make(BComponent srcComponent,
                                     String name,
                                     String label,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly,
                                     BBacnetObjectIdentifier bacnetObjectId
    ) {
        boolean boolValue = ((BIHonWizardBooleanPoint)srcComponent).getValue();
        boolean boolDefault = ((BIHonWizardBooleanPoint)srcComponent).getDefault();

        BSwitchButton result = make(name, boolValue, boolDefault, label, tabInPage, help, inline, readOnly);
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");

        return result;
    }

    public static BSwitchButton make(String name,
                                     boolean boolValue,
                                     boolean boolDefault,
                                     String label,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly,
                                     BOrd commonSlot
    ) {
        BSwitchButton result = make(name, boolValue, boolDefault, label, tabInPage, help, inline, readOnly);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");

        return result;
    }

    private static BSwitchButton make(String name,
                                     boolean boolValue,
                                     boolean boolDefault,
                                     String label,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly
    ) {
        BSwitchButton switchButton = new BSwitchButton();
        switchButton.setItemName(name);
        switchButton.setLabel(label);
        int value = boolValue ? 1 : 0;
        int defaultValue = boolDefault ? 1 : 0;
        switchButton.setValue(value);
        switchButton.setDefaultValue(defaultValue);
        switchButton.setVisible(true);
        switchButton.setTabInPage(tabInPage == null ? "" : tabInPage);
        switchButton.setHelp(help == null ? "" : help);
        switchButton.setInline(inline);
        switchButton.setReadOnly(readOnly);
        switchButton.setLoadConvert(getLoadConvertFunc());
        switchButton.setSaveConvert(getSaveConvertFunc());

        return switchButton;
    }

    private static String getLoadConvertFunc() {
        return "function loadConvert(propertyValue) {\n" +
                "return propertyValue == 1 || propertyValue == true;\n" +
                "}";
    }

    private static String getSaveConvertFunc() {
        return "function saveConvert(value) {\n" +
                "return value == true || value == 1 ? 1 : 0;\n" +
                "}";
    }

    @Override
    public void setValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BSwitchButton (" + getLabel() + "): " + value);
        }
    }
    
    @Override
    public void setDefaultValue(String value) {
        try {
            int intValue = Integer.parseInt(value);
            setDefaultValue(intValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid default value for BSwitchButton (" + getLabel() + "): " + value);
        }
    }
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(Integer.toString(getValue()));
	}
    
}