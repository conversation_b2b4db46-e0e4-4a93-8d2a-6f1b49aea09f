/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import java.util.logging.Logger;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "0.0", type = "double")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "componentType", defaultValue = "SingleSlider", type = "String", flags = Flags.READONLY)
@NiagaraProperty (name = "color", defaultValue = "", type = "String")
@NiagaraType
public class BSingleSlider extends BDynamicWidgetUnitSupportComponentBase{
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BSingleSlider(364046698)1.0$ @*/
/* Generated Fri Mar 14 11:36:10 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);

  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }

  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, 0.0, null);

  /**
   * Get the {@code value} property.
   * @see #value
   */
  public double getValue() { return getDouble(value); }

  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(double v) { setDouble(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);

  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }

  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, "SingleSlider", null);

  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }

  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Property "color"
////////////////////////////////////////////////////////////////

  /**
   * Slot for the {@code color} property.
   * @see #getColor
   * @see #setColor
   */
  public static final Property color = newProperty(0, "", null);

  /**
   * Get the {@code color} property.
   * @see #color
   */
  public String getColor() { return getString(color); }

  /**
   * Set the {@code color} property.
   * @see #color
   */
  public void setColor(String v) { setString(color, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

  @Override
  public Type getType() { return TYPE; }



    public static final Type TYPE = Sys.loadType(BSingleSlider.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BSingleSlider make(BComponent srcComponent,
                                     String name,
                                     String label,
                                     String color,
                                     String tabInPage,
                                     String help,
                                     double minValue,
                                     double maxValue,
                                     double step,
                                     int precision,
                                     String inline,
                                     Boolean readOnly,
                                     BBacnetObjectIdentifier bacnetObjectId,
                                     String unitGroup

    ) {
        //TODO: value will always be double??
        double value = ((BIHonWizardNumericPoint) srcComponent).getValue();
        double defaultValue = ((BIHonWizardNumericPoint) srcComponent).getDefault();
        BUnit unit = ((BIHonWizardNumericPoint) srcComponent).getUnit();


		BSingleSlider result = make(name, value, defaultValue, unit, step, precision, label, color, tabInPage, help, minValue, maxValue, inline,
				readOnly, String.valueOf(value), String.valueOf(minValue), String.valueOf(maxValue), String.valueOf(defaultValue),
				unitGroup);
        result.setBacnetObjectId(bacnetObjectId);
        result.hideSlot("commonSlot");

        return result;
    }

    public static BSingleSlider make(String name,
                                      double value,
                                      double defaultValue,
                                      BUnit unit,
                                      double step,
                                      int precision,
                                      String label,
                                      String color,
                                      String tabInPage,
                                      String help,
                                      double minValue,
                                      double maxValue,
                                      String inline,
                                      Boolean readOnly,
                                      BOrd commonSlot,
                                      String highPrecisionValue,
                                      String highPrecisionMin,
                                      String highPrecisionMax,
                                      String highPrecisionDefaultValue,
                                      String unitGroup

    ) {
		BSingleSlider result = make(name, value, defaultValue, unit, step, precision, label, color, tabInPage, help, minValue, maxValue, inline,
				readOnly, highPrecisionValue, highPrecisionMin, highPrecisionMax, highPrecisionDefaultValue, unitGroup);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");

        return result;
    }

    private static BSingleSlider make(String name,
                                     double value,
                                     double defaultValue,
                                     BUnit unit,
                                     double step,
                                     int precision,
                                     String label,
                                     String color,
                                     String tabInPage,
                                     String help,
                                     double minValue,
                                     double maxValue,
                                     String inline,
                                     Boolean readOnly,
                                     String highPrecisionValue,
                                     String highPrecisionMin,
                                     String highPrecisionMax,
                                     String highPrecisionDefaultValue,
                                     String unitGroup
    ) {
        BSingleSlider singleSlider = new BSingleSlider();
        singleSlider.setItemName(name);
        singleSlider.setLabel(label);
        singleSlider.setValue(value);
        singleSlider.setDefaultValue(defaultValue);
        singleSlider.setVisible(true);
        singleSlider.setColor(color != null ? color : "");
        //How to get it? from tags??
        singleSlider.setMin(minValue);
        //How to get it? from tags??
        singleSlider.setMax(maxValue);
        singleSlider.setTabInPage(tabInPage == null ? "" : tabInPage);
        singleSlider.setHelp(help == null ? "" : help);
        singleSlider.setInline(inline);
        singleSlider.setReadOnly(readOnly);
        singleSlider.setUnit(unit == null || "null".equals(unit.getSymbol()) ? "" : unit.getSymbol());
        if(null != unit) {
        	singleSlider.setFacets(BSingleSlider.unit, BFacets.make(BFacets.UNITS, unit));
        }
        singleSlider.setHighPrecisionValue(highPrecisionValue);

        singleSlider.setHighPrecisionMin(highPrecisionMin);
        singleSlider.setHighPrecisionMax(highPrecisionMax);
        singleSlider.setHighPrecisionDefaultValue(highPrecisionDefaultValue);
        singleSlider.setStep(step);
        singleSlider.setPrecision(precision);
        
		// Performed direct enum tag comparison as EnumRange is missing due to
		// current Niagara limitations.
		if (null != unitGroup && !unitGroup.isEmpty()) {
			if (BUnitGroupEnum.measurementType.getDisplayTag(null).equals(unitGroup)) {
				singleSlider.setUnitGroup(BUnitGroupEnum.measurementType);
			} else if(BUnitGroupEnum.airflowUnit.getDisplayTag(null).equals(unitGroup)) {
				singleSlider.setUnitGroup(BUnitGroupEnum.airflowUnit);
			} else {
				singleSlider.setUnitGroup(BUnitGroupEnum.none);
			}
		}
        return singleSlider;
    }

    @Override
    public void setValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setValue(doubleValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid value for BSingleSlider (" + getLabel() + "): " + value);
        }
    }

    @Override
    public void setDefaultValue(String value) {
        try {
            double doubleValue = Double.parseDouble(value);
            setDefaultValue(doubleValue);
        } catch (NumberFormatException e) {
            logger.severe("Invalid default value for BSingleSlider (" + getLabel() + "): " + value);
        }
    }

    @Override
	public BString getValueFromWizComp() {
		return BString.make(Double.toString(getValue()));
	}


}
