/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.bacnet.datatypes.BBacnetObjectIdentifier;
import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.Facet;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 *
 * <AUTHOR> - <PERSON><PERSON><PERSON><PERSON><PERSON> Madhusudhan
 * @since Jan 29, 2025
 */
@NiagaraProperty(
        name = "bacnetObjectId",
        type = "BBacnetObjectIdentifier",
        defaultValue = "BBacnetObjectIdentifier.DEFAULT",
        flags = Flags.READONLY
)
@NiagaraProperty (name = "tabInPage", defaultValue = "", type = "String")
@NiagaraProperty (name = "inline", defaultValue = "", type = "String")
@NiagaraProperty (name = "commonSlot", defaultValue = "BOrd.DEFAULT", type = "BOrd")
@NiagaraProperty (name = "readOnly", defaultValue = "false" , type = "boolean", facets = {
        @Facet(name = "BFacets.FALSE_TEXT", value = "BString.make(\"No\")"),
        @Facet(name = "BFacets.TRUE_TEXT", value = "BString.make(\"Yes\")")
})
@NiagaraProperty (name = "help", defaultValue = "" , type = "String")
@NiagaraProperty (name = "itemName", defaultValue = "", type = "String")
@NiagaraType
public abstract class BDynamicWidgetComponentBase extends BWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase(*********)1.0$ @*/
/* Generated Mon Apr 14 15:30:06 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "bacnetObjectId"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code bacnetObjectId} property.
   * @see #getBacnetObjectId
   * @see #setBacnetObjectId
   */
  public static final Property bacnetObjectId = newProperty(Flags.READONLY, BBacnetObjectIdentifier.DEFAULT, null);
  
  /**
   * Get the {@code bacnetObjectId} property.
   * @see #bacnetObjectId
   */
  public BBacnetObjectIdentifier getBacnetObjectId() { return (BBacnetObjectIdentifier)get(bacnetObjectId); }
  
  /**
   * Set the {@code bacnetObjectId} property.
   * @see #bacnetObjectId
   */
  public void setBacnetObjectId(BBacnetObjectIdentifier v) { set(bacnetObjectId, v, null); }

////////////////////////////////////////////////////////////////
// Property "tabInPage"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code tabInPage} property.
   * @see #getTabInPage
   * @see #setTabInPage
   */
  public static final Property tabInPage = newProperty(0, "", null);
  
  /**
   * Get the {@code tabInPage} property.
   * @see #tabInPage
   */
  public String getTabInPage() { return getString(tabInPage); }
  
  /**
   * Set the {@code tabInPage} property.
   * @see #tabInPage
   */
  public void setTabInPage(String v) { setString(tabInPage, v, null); }

////////////////////////////////////////////////////////////////
// Property "inline"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code inline} property.
   * @see #getInline
   * @see #setInline
   */
  public static final Property inline = newProperty(0, "", null);
  
  /**
   * Get the {@code inline} property.
   * @see #inline
   */
  public String getInline() { return getString(inline); }
  
  /**
   * Set the {@code inline} property.
   * @see #inline
   */
  public void setInline(String v) { setString(inline, v, null); }

////////////////////////////////////////////////////////////////
// Property "commonSlot"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code commonSlot} property.
   * @see #getCommonSlot
   * @see #setCommonSlot
   */
  public static final Property commonSlot = newProperty(0, BOrd.DEFAULT, null);
  
  /**
   * Get the {@code commonSlot} property.
   * @see #commonSlot
   */
  public BOrd getCommonSlot() { return (BOrd)get(commonSlot); }
  
  /**
   * Set the {@code commonSlot} property.
   * @see #commonSlot
   */
  public void setCommonSlot(BOrd v) { set(commonSlot, v, null); }

////////////////////////////////////////////////////////////////
// Property "readOnly"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code readOnly} property.
   * @see #getReadOnly
   * @see #setReadOnly
   */
  public static final Property readOnly = newProperty(0, false, BFacets.make(BFacets.make(BFacets.FALSE_TEXT, BString.make("No")), BFacets.make(BFacets.TRUE_TEXT, BString.make("Yes"))));
  
  /**
   * Get the {@code readOnly} property.
   * @see #readOnly
   */
  public boolean getReadOnly() { return getBoolean(readOnly); }
  
  /**
   * Set the {@code readOnly} property.
   * @see #readOnly
   */
  public void setReadOnly(boolean v) { setBoolean(readOnly, v, null); }

////////////////////////////////////////////////////////////////
// Property "help"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code help} property.
   * @see #getHelp
   * @see #setHelp
   */
  public static final Property help = newProperty(0, "", null);
  
  /**
   * Get the {@code help} property.
   * @see #help
   */
  public String getHelp() { return getString(help); }
  
  /**
   * Set the {@code help} property.
   * @see #help
   */
  public void setHelp(String v) { setString(help, v, null); }

////////////////////////////////////////////////////////////////
// Property "itemName"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code itemName} property.
   * @see #getItemName
   * @see #setItemName
   */
  public static final Property itemName = newProperty(0, "", null);
  
  /**
   * Get the {@code itemName} property.
   * @see #itemName
   */
  public String getItemName() { return getString(itemName); }
  
  /**
   * Set the {@code itemName} property.
   * @see #itemName
   */
  public void setItemName(String v) { setString(itemName, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDynamicWidgetComponentBase.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    public abstract void setValue(String value);
    public abstract void setDefaultValue(String value);
    public void makeOptions(BEnumRange tagEnumRange, BEnumRange targetEnumRange) {}

    protected void hideSlot(String slotName) {
        Slot slot = getSlot(slotName);
        if(slot != null) {
            setFlags(getSlot(slotName), Flags.HIDDEN);
        }
    }
}
