/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.widgetcomponents;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BFacets;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.units.BUnit;

import com.honeywell.applicationhandler.enums.BUnitGroupEnum;

/**
 * <AUTHOR> zhang
 */

@NiagaraProperty(name = "unit", defaultValue = "", type = "String")
@NiagaraProperty(name = "min", defaultValue = "0.0", type = "double")
@NiagaraProperty(name = "max", defaultValue = "100.0", type = "double")
@NiagaraProperty (name = "defaultValue", defaultValue = "0.0", type = "double")
@NiagaraProperty(name = "highPrecisionValue", defaultValue = "", type = "String")
@NiagaraProperty(name = "highPrecisionMax", defaultValue = "", type = "String")
@NiagaraProperty(name = "highPrecisionMin", defaultValue = "", type = "String")
@NiagaraProperty(name = "highPrecisionDefaultValue", defaultValue = "", type = "String")
@NiagaraProperty(name = "unitGroup", defaultValue = "BUnitGroupEnum.measurementType", type = "BUnitGroupEnum")
@NiagaraProperty(name = "step", defaultValue = "1", type = "double")
@NiagaraProperty(name = "precision", defaultValue = "0", type = "int")
@NiagaraProperty(name = "hasUnitChanged", defaultValue = "false", type = "boolean")
@NiagaraType
public abstract class BDynamicWidgetUnitSupportComponentBase extends BDynamicWidgetComponentBase {
    
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetUnitSupportComponentBase(3606738724)1.0$ @*/
/* Generated Tue Jul 15 17:19:12 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "unit"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code unit} property.
   * @see #getUnit
   * @see #setUnit
   */
  public static final Property unit = newProperty(0, "", null);
  
  /**
   * Get the {@code unit} property.
   * @see #unit
   */
  public String getUnit() { return getString(unit); }
  
  /**
   * Set the {@code unit} property.
   * @see #unit
   */
  public void setUnit(String v) { setString(unit, v, null); }

////////////////////////////////////////////////////////////////
// Property "min"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code min} property.
   * @see #getMin
   * @see #setMin
   */
  public static final Property min = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code min} property.
   * @see #min
   */
  public double getMin() { return getDouble(min); }
  
  /**
   * Set the {@code min} property.
   * @see #min
   */
  public void setMin(double v) { setDouble(min, v, null); }

////////////////////////////////////////////////////////////////
// Property "max"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code max} property.
   * @see #getMax
   * @see #setMax
   */
  public static final Property max = newProperty(0, 100.0, null);
  
  /**
   * Get the {@code max} property.
   * @see #max
   */
  public double getMax() { return getDouble(max); }
  
  /**
   * Set the {@code max} property.
   * @see #max
   */
  public void setMax(double v) { setDouble(max, v, null); }

////////////////////////////////////////////////////////////////
// Property "defaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code defaultValue} property.
   * @see #getDefaultValue
   * @see #setDefaultValue
   */
  public static final Property defaultValue = newProperty(0, 0.0, null);
  
  /**
   * Get the {@code defaultValue} property.
   * @see #defaultValue
   */
  public double getDefaultValue() { return getDouble(defaultValue); }
  
  /**
   * Set the {@code defaultValue} property.
   * @see #defaultValue
   */
  public void setDefaultValue(double v) { setDouble(defaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "highPrecisionValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code highPrecisionValue} property.
   * @see #getHighPrecisionValue
   * @see #setHighPrecisionValue
   */
  public static final Property highPrecisionValue = newProperty(0, "", null);
  
  /**
   * Get the {@code highPrecisionValue} property.
   * @see #highPrecisionValue
   */
  public String getHighPrecisionValue() { return getString(highPrecisionValue); }
  
  /**
   * Set the {@code highPrecisionValue} property.
   * @see #highPrecisionValue
   */
  public void setHighPrecisionValue(String v) { setString(highPrecisionValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "highPrecisionMax"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code highPrecisionMax} property.
   * @see #getHighPrecisionMax
   * @see #setHighPrecisionMax
   */
  public static final Property highPrecisionMax = newProperty(0, "", null);
  
  /**
   * Get the {@code highPrecisionMax} property.
   * @see #highPrecisionMax
   */
  public String getHighPrecisionMax() { return getString(highPrecisionMax); }
  
  /**
   * Set the {@code highPrecisionMax} property.
   * @see #highPrecisionMax
   */
  public void setHighPrecisionMax(String v) { setString(highPrecisionMax, v, null); }

////////////////////////////////////////////////////////////////
// Property "highPrecisionMin"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code highPrecisionMin} property.
   * @see #getHighPrecisionMin
   * @see #setHighPrecisionMin
   */
  public static final Property highPrecisionMin = newProperty(0, "", null);
  
  /**
   * Get the {@code highPrecisionMin} property.
   * @see #highPrecisionMin
   */
  public String getHighPrecisionMin() { return getString(highPrecisionMin); }
  
  /**
   * Set the {@code highPrecisionMin} property.
   * @see #highPrecisionMin
   */
  public void setHighPrecisionMin(String v) { setString(highPrecisionMin, v, null); }

////////////////////////////////////////////////////////////////
// Property "highPrecisionDefaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code highPrecisionDefaultValue} property.
   * @see #getHighPrecisionDefaultValue
   * @see #setHighPrecisionDefaultValue
   */
  public static final Property highPrecisionDefaultValue = newProperty(0, "", null);
  
  /**
   * Get the {@code highPrecisionDefaultValue} property.
   * @see #highPrecisionDefaultValue
   */
  public String getHighPrecisionDefaultValue() { return getString(highPrecisionDefaultValue); }
  
  /**
   * Set the {@code highPrecisionDefaultValue} property.
   * @see #highPrecisionDefaultValue
   */
  public void setHighPrecisionDefaultValue(String v) { setString(highPrecisionDefaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "unitGroup"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code unitGroup} property.
   * @see #getUnitGroup
   * @see #setUnitGroup
   */
  public static final Property unitGroup = newProperty(0, BUnitGroupEnum.measurementType, null);
  
  /**
   * Get the {@code unitGroup} property.
   * @see #unitGroup
   */
  public BUnitGroupEnum getUnitGroup() { return (BUnitGroupEnum)get(unitGroup); }
  
  /**
   * Set the {@code unitGroup} property.
   * @see #unitGroup
   */
  public void setUnitGroup(BUnitGroupEnum v) { set(unitGroup, v, null); }

////////////////////////////////////////////////////////////////
// Property "step"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code step} property.
   * @see #getStep
   * @see #setStep
   */
  public static final Property step = newProperty(0, 1, null);
  
  /**
   * Get the {@code step} property.
   * @see #step
   */
  public double getStep() { return getDouble(step); }
  
  /**
   * Set the {@code step} property.
   * @see #step
   */
  public void setStep(double v) { setDouble(step, v, null); }

////////////////////////////////////////////////////////////////
// Property "precision"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code precision} property.
   * @see #getPrecision
   * @see #setPrecision
   */
  public static final Property precision = newProperty(0, 0, null);
  
  /**
   * Get the {@code precision} property.
   * @see #precision
   */
  public int getPrecision() { return getInt(precision); }
  
  /**
   * Set the {@code precision} property.
   * @see #precision
   */
  public void setPrecision(int v) { setInt(precision, v, null); }

////////////////////////////////////////////////////////////////
// Property "hasUnitChanged"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code hasUnitChanged} property.
   * @see #getHasUnitChanged
   * @see #setHasUnitChanged
   */
  public static final Property hasUnitChanged = newProperty(0, false, null);
  
  /**
   * Get the {@code hasUnitChanged} property.
   * @see #hasUnitChanged
   */
  public boolean getHasUnitChanged() { return getBoolean(hasUnitChanged); }
  
  /**
   * Set the {@code hasUnitChanged} property.
   * @see #hasUnitChanged
   */
  public void setHasUnitChanged(boolean v) { setBoolean(hasUnitChanged, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDynamicWidgetUnitSupportComponentBase.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    public void updateUnitRelatedValues(String unitSymbol, String unitName, String highPrecisionValue,
                                        String highPrecisionMin, String highPrecisionMax, String highPrecisionDefaultValue,
                                        double min, double max, double defaultValue, double step, int precision) {
    	BUnit bUnit=null;
		if (null != unitName) {
			bUnit = BUnit.getUnit(unitName);
			setHasUnitChanged(getUnit().equals(bUnit.getSymbol()));
			this.setUnit(bUnit == null || "null".equals(bUnit.getSymbol()) ? "" : bUnit.getSymbol());
		}
        this.setHighPrecisionValue(highPrecisionValue);
        this.setHighPrecisionDefaultValue(highPrecisionDefaultValue);
        this.setHighPrecisionMin(highPrecisionMin);
        this.setHighPrecisionMax(highPrecisionMax);
        this.setMin(min);
        this.setMax(max);
        this.setStep(step);
        this.setPrecision(precision);
        this.setDefaultValue(defaultValue);
        // No need to updated unitGroup as it is static data and will not change

        Slot unitSlot = this.getSlot("unit");
        BFacets unitFacets = this.getSlotFacets(unitSlot);
        if (null != unitFacets && null != unitFacets.getFacet(BFacets.UNITS) && null != bUnit) {
            BFacets updatedFacets = BFacets.makeRemove(unitFacets, BFacets.UNITS);
            updatedFacets = BFacets.make(updatedFacets, BFacets.UNITS, bUnit);
            this.setFacets(unitSlot, updatedFacets);
        }

    }
}
