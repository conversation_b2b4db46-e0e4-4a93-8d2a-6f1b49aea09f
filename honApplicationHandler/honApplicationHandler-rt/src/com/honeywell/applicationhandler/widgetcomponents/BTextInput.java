/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.widgetcomponents;

import java.util.logging.Logger;

import javax.baja.naming.BOrd;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BString;
import javax.baja.sys.BasicContext;
import javax.baja.sys.Context;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import com.honeywell.applicationhandler.utils.HoneywellDeviceWizardLogger;

/**
 * <AUTHOR> Sun
 */
@NiagaraProperty (name = "label", defaultValue = "", type = "String")
@NiagaraProperty (name = "value", defaultValue = "", type = "String")
@NiagaraProperty (name = "visible", defaultValue = "true", type = "boolean")
@NiagaraProperty (name = "defaultValue", defaultValue = "", type = "String")
@NiagaraProperty (name = "componentType", defaultValue = "TextInput", type = "String", flags = Flags.READONLY)
@NiagaraType
public class BTextInput extends BDynamicWidgetComponentBase {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.widgetcomponents.BTextInput(4123280167)1.0$ @*/
/* Generated Sun Feb 09 13:35:15 IST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "label"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code label} property.
   * @see #getLabel
   * @see #setLabel
   */
  public static final Property label = newProperty(0, "", null);
  
  /**
   * Get the {@code label} property.
   * @see #label
   */
  public String getLabel() { return getString(label); }
  
  /**
   * Set the {@code label} property.
   * @see #label
   */
  public void setLabel(String v) { setString(label, v, null); }

////////////////////////////////////////////////////////////////
// Property "value"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code value} property.
   * @see #getValue
   * @see #setValue
   */
  public static final Property value = newProperty(0, "", null);
  
  /**
   * Get the {@code value} property.
   * @see #value
   */
  public String getValue() { return getString(value); }
  
  /**
   * Set the {@code value} property.
   * @see #value
   */
  public void setValue(String v) { setString(value, v, null); }

////////////////////////////////////////////////////////////////
// Property "visible"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code visible} property.
   * @see #getVisible
   * @see #setVisible
   */
  public static final Property visible = newProperty(0, true, null);
  
  /**
   * Get the {@code visible} property.
   * @see #visible
   */
  public boolean getVisible() { return getBoolean(visible); }
  
  /**
   * Set the {@code visible} property.
   * @see #visible
   */
  public void setVisible(boolean v) { setBoolean(visible, v, null); }

////////////////////////////////////////////////////////////////
// Property "defaultValue"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code defaultValue} property.
   * @see #getDefaultValue
   * @see #setDefaultValue
   */
  public static final Property defaultValue = newProperty(0, "", null);
  
  /**
   * Get the {@code defaultValue} property.
   * @see #defaultValue
   */
  public String getDefaultValue() { return getString(defaultValue); }
  
  /**
   * Set the {@code defaultValue} property.
   * @see #defaultValue
   */
  public void setDefaultValue(String v) { setString(defaultValue, v, null); }

////////////////////////////////////////////////////////////////
// Property "componentType"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code componentType} property.
   * @see #getComponentType
   * @see #setComponentType
   */
  public static final Property componentType = newProperty(Flags.READONLY, "TextInput", null);
  
  /**
   * Get the {@code componentType} property.
   * @see #componentType
   */
  public String getComponentType() { return getString(componentType); }
  
  /**
   * Set the {@code componentType} property.
   * @see #componentType
   */
  public void setComponentType(String v) { setString(componentType, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BTextInput.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    static Logger logger = HoneywellDeviceWizardLogger.getLogger();

    public static BTextInput make(String name,
                                    String value,
                                    String defaultValue,
                                    String label,
                                    String tabInPage,
                                    String help,
                                    String inline,
                                    Boolean readOnly,
                                    BOrd commonSlot,
                                    BEnumRange enumRange
    ) {
        BTextInput result = make(name, value, defaultValue, label, tabInPage, help, inline, readOnly, enumRange);
        result.setCommonSlot(commonSlot);
        result.hideSlot("bacnetObjectId");

        return result;
    }

    private static BTextInput make(String name,
                                     String value,
                                     String defaultValue,
                                     String label,
                                     String tabInPage,
                                     String help,
                                     String inline,
                                     Boolean readOnly,
                                     BEnumRange enumRange
                                     
    ) {
        BTextInput textInput = new BTextInput();
        textInput.setItemName(name);
        textInput.setLabel(label);
        textInput.setValue(value);
        textInput.setDefaultValue(defaultValue);
        if(null != enumRange) {
        	BFacets range = BFacets.make(BFacets.RANGE, enumRange);
        	textInput.setFacets(BTextInput.value, range);
        	textInput.setFacets(BTextInput.defaultValue, range);
        }
        textInput.setVisible(true);
        textInput.setTabInPage(tabInPage == null ? "" : tabInPage);
        textInput.setHelp(help == null ? "" : help);
        textInput.setInline(inline);
        textInput.setReadOnly(readOnly);

        return textInput;
    }
    
    @Override
	public void changed(Property property, Context context) {
		if (null != context && context.equals(CHANGE_CTX)) {
			return;
		}
		super.changed(property, context);
		if (property.equals(BTextInput.value) && null != this.getSlotFacets(getSlot(value.getName())).get(BFacets.RANGE)) {
			int val;
			try {
				val = Integer.parseInt(getValue());
			} catch (NumberFormatException e) {
				return;
			}
			BEnumRange range = (BEnumRange) this.getSlotFacets(getSlot(value.getName())).get(BFacets.RANGE);
			set(value, BString.make(range.get(val).getTag()), CHANGE_CTX);
		} else if (property.equals(BTextInput.defaultValue) && null != this.getSlotFacets(getSlot(defaultValue.getName())).get(BFacets.RANGE)) {
			int val;
			try {
				val = Integer.parseInt(getDefaultValue());
			} catch (NumberFormatException e) {
				return;
			}
			BEnumRange range = (BEnumRange) this.getSlotFacets(getSlot(defaultValue.getName())).get(BFacets.RANGE);
			set(defaultValue, BString.make(range.get(val).getTag()), CHANGE_CTX);
		}
	}
    
    @Override
	public BString getValueFromWizComp() {
		return BString.make(getValue());
	}
    
    private static final BasicContext CHANGE_CTX = new BasicContext();
    
}
