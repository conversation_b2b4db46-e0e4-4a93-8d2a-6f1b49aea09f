/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of <PERSON><PERSON>.
 */

package com.honeywell.applicationhandler.utils;

import com.honeywell.applicationhandler.device.BHonWizardGlobalStore;
import com.honeywell.applicationhandler.device.BHonWizardRuleStore;
import com.honeywell.applicationhandler.device.BIHoneywellConfigurableDevice;
import com.honeywell.applicationhandler.device.points.BIHonWizardNumericPoint;
import com.honeywell.applicationhandler.device.points.BIHonWizardPoint;
import com.honeywell.applicationhandler.ontology.BHonWizardTag;
import com.honeywell.applicationhandler.ontology.Const;
import com.honeywell.applicationhandler.ontology.selector.BHonWizPointSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSelector;
import com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector;
import com.honeywell.applicationhandler.widgetcomponents.BDuctAreaCalculator;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;

import javax.baja.collection.BITable;
import javax.baja.data.BIDataValue;
import javax.baja.naming.BOrd;
import javax.baja.naming.SlotPath;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BComplex;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BFacets;
import javax.baja.sys.BStation;
import javax.baja.sys.BObject;
import javax.baja.sys.BString;
import javax.baja.sys.BValue;
import javax.baja.sys.Cursor;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Slot;
import javax.baja.sys.Sys;
import javax.baja.tag.Id;
import javax.baja.tag.Tag;
import javax.baja.units.BUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import static com.honeywell.applicationhandler.common.Constants.DUCT_AREA_CALCULATOR_TYPE;
import static com.honeywell.applicationhandler.ontology.Const.CELSIUS;

/**
 * The HoneywellConfigurableDeviceUtil class provides utility methods for configuring and managing
 * Honeywell devices within the application. It handles the generation of global store configurations,
 * slot details, and updates for Honeywell devices. This class interacts with various components and
 * selectors to build and update the device configuration.

 * <p>Key functionalities include:</p>
 * <ul>
 *   <li>Get multiple tag values by given component</li>
 *   <li>Get all BIHonWizardPoints under given device</li>
 *   <li>Get all BHonWizardSelectors under given device</li>
 *   <li>Sorting slot paths based on order tags.</li>
 *   <li>Cleaning up existing slot and point selector tags.</li>
 * </ul>
 *
 * <p>Dependencies:</p>
 * <ul>
 *   <li>BIHonWizardPoint: Represents a point that can be configured into the wizard.</li>
 *   <li>BHonWizardTag: Represents a tag used to collect data required by the wizard.</li>
 *   <li>BHonWizSelector: Represents a selector used in the wizard configuration.</li>
 *   <li>BHonWizSlotSelector: Represents a slot selector used in the wizard configuration.</li>
 *   <li>BHonWizPointSelector: Represents a point selector used in the wizard configuration.</li>
 * </ul>
 *
 * <p>Usage:</p>
 * <pre>
 * {@code
 * BIHoneywellConfigurableDevice device = ...;
 * List<BIHonWizardPoint> allHonWizardPoints = HoneywellConfigurableDeviceUtil.getAllHonWizardPoints((BComponent)device);
 * }
 * </pre>
 *
 */
public class HoneywellConfigurableDeviceUtil {

    public static BEnumRange getOptionsFromTag(BComponent cp) {
        Iterator<Tag> itr = cp.tags().iterator();
        while (itr.hasNext()) {
            Tag currentTag = itr.next();
            Id tagId = currentTag.getId();
            if(tagId.equals(BHonWizardTag.WIZ_OPTIONS_TAG)) {
                BIDataValue tagValue = currentTag.getValue();
                return (BEnumRange) tagValue;
            }
        }

        return null;
    }

    public static String getTransferNumberListToBitwiseValueFuncString() {
        return "function f(nl) {\n" +
                "var bv = 0;\n" +
                "nl.forEach(n => {bv = bv | n;});\n" +
                "return bv;\n" +
                "}";
    }

    public static String getTransferBitwiseValueToNumberListFuncString() {
        return "function f(v) {\n" +
                "var r = [];\n" +
                "for(var i = 0; i<8; i++) {\n" +
                "var fa = 1 << i;\n" +
                "var bv = v & fa;\n" +
                "if(bv) {r.push(bv);}\n" +
                "} return r;}";
    }

    public static Map<String, BIDataValue> getTagValues(BComponent cp, List<String> tagNames) {
        Map<String, BIDataValue> result = new HashMap<>();
        for (Tag currentTag : cp.tags()) {
            Id currentTagId = currentTag.getId();
            if((tagNames == null && currentTagId.getDictionary().equals(Const.HON_WIZARD_TAG_NAMESPACE))
                    || (tagNames != null && tagNames.contains(currentTagId.getName()))) {
                BIDataValue tagValue = currentTag.getValue();
                result.put(currentTagId.getName(), tagValue);
            }
        }

        return result;
    }

    /**
     * filter tag values which is not null or empty
     * @param cp
     * @param tagNames
     * @return
     */
    public static Map<String, BIDataValue> getNotNullTagValues(BComponent cp, List<String> tagNames) {
        Map<String, BIDataValue> result = new HashMap<>();
        for (Tag currentTag : cp.tags()) {
            Id currentTagId = currentTag.getId();
            if((tagNames == null && currentTagId.getDictionary().equals(Const.HON_WIZARD_TAG_NAMESPACE))
                    || (tagNames != null && tagNames.contains(currentTagId.getName()))) {
                BIDataValue tagValue = currentTag.getValue();
                if (tagValue == null || (tagValue instanceof BEnumRange && ((BEnumRange) tagValue).isNull()) ||  tagValue.toString().isEmpty()) {
                    continue;
                }
                result.put(currentTagId.getName(), tagValue);
            }
        }

        return result;
    }

    public static Double getOrderFromTag(BComponent device, SlotPath slotPath) {
        BOrd ord = BOrd.make(slotPath.toString());
        BComponent cp = (BComponent)ord.get(device);
        Iterator<Tag> itr = cp.tags().iterator();
        while (itr.hasNext()) {
            Tag tag = itr.next();
            Id tagId = tag.getId();
            if(tagId.equals(BHonWizardTag.WIZ_ORDER_TAG)) {
                BIDataValue tagValue = tag.getValue();
                return Double.valueOf(tagValue.toString());
            }
        }

        return 0.0;
    }

    public static String getStringTagValueOfComponent(BComponent cp, Id tagId) {
        Iterator<Tag> itr = cp.tags().iterator();
        while (itr.hasNext()) {
            Tag currentTag = itr.next();
            Id currentTagId = currentTag.getId();
            if(currentTagId.equals(tagId)) {
                BIDataValue tagValue = currentTag.getValue();
                return tagValue.toString();
            }
        }

        return null;
    }
    public static String getWidgetTypeStringFromTagValues(Map<String, BIDataValue> tagValues) {
        BDynamicEnum biDataValue = (BDynamicEnum) tagValues.get(BHonWizardTag.HON_WIZ_WDTTYPE_TAG);
        return biDataValue.getTag();

    }

    public static Boolean getBooleanTagValueOfComponent(BComponent cp, Id tagId) {
        Iterator<Tag> itr = cp.tags().iterator();
        while (itr.hasNext()) {
            Tag currentTag = itr.next();
            Id currentTagId = currentTag.getId();
            if(currentTagId.equals(tagId)) {
                BIDataValue tagValue = currentTag.getValue();
                if(tagValue instanceof BBoolean) {
                    return ((BBoolean) tagValue).getBoolean();
                }
                return null;
            }
        }

        return null;
    }

    public static List<SlotPath> sortByOrder(BComponent device, List<SlotPath> pointSlotPaths) {
        Collections.sort(pointSlotPaths, new Comparator<SlotPath>() {
            @Override
            public int compare(SlotPath sp1, SlotPath sp2) {
                Double order1 = getOrderFromTag(device, sp1);
                Double order2 = getOrderFromTag(device, sp2);

                return order1.compareTo(order2);
            }});
        return pointSlotPaths;
    }

    public static List<BIHonWizardPoint> getAllHonWizardPoints(BComponent device) {
        List<BIHonWizardPoint> points = new ArrayList<>();

        BOrd pointsOrd = BOrd.make(device.getNavOrd().toString() + "|bql:select from honApplicationHandler:IHonWizardPoint" );
        BITable<?> result = (BITable<?>) pointsOrd.resolve(Sys.getStation()).get();

        Cursor<?> c = result.cursor();

        while (c.next()) {
            BIHonWizardPoint point = (BIHonWizardPoint) c.get();
            points.add(point);
        }

        return points;
    }

    public static List<BHonWizSelector> getAllHonWizardSelectors(BComponent device) {
        String bqlQuery = "bql:select from honApplicationHandler:HonWizSelector";
        return queryHonWizSelectors(device, bqlQuery);
    }

    /**
     * get all hon wizard point selector
     * @param device
     * @return
     */
    public static List<BHonWizSelector> getAllHonWizardPointSelectors(BComponent device) {
        String bqlQuery = "bql:select from honApplicationHandler:HonWizPointSelector";
        return queryHonWizSelectors(device, bqlQuery);
    }

    public static List<BHonWizSelector> getHonWizSelectorsWithTags(BComponent device, Map<String, Object> tagValues) {
        String neqlQuery = buildNeqlForSelector(tagValues);
        return queryHonWizSelectors(device, neqlQuery);
    }

    private static List<BHonWizSelector> queryHonWizSelectors(BComponent device, String query) {
        List<BHonWizSelector> selectors = new ArrayList<>();
        if(null == device.getNavOrd()){
            return selectors; // Return empty list if device has no navigation order
        }

        BOrd selectorsOrd = BOrd.make(device.getNavOrd().toString() + "|" + query );
        BITable<?> result = (BITable<?>) selectorsOrd.resolve(Sys.getStation()).get();

        Cursor<?> c = result.cursor();

        while (c.next()) {
            BHonWizSelector selector = (BHonWizSelector) c.get();
            selectors.add(selector);
        }

        return selectors;
    }

    private static String buildNeqlForSelector(Map<String, Object> tagValues) {
        StringBuilder neql = new StringBuilder();
        neql.append("neql:");
        tagValues.forEach((tagName, value) -> {
            neql.append(Const.HON_WIZARD_TAG_NAMESPACE).append(":").append(tagName);
            if(value instanceof String) {
                neql.append("=\"").append(value).append("\"");
            }
            else {
                neql.append("=").append(value);
            }
            neql.append(" and ");
        });

        neql.append("(n:type=\"honApplicationHandler:HonWizSlotSelector\" or n:type=\"honApplicationHandler:HonWizPointSelector\")");
        return neql.toString();
    }

	public static void cleanUpExistingHonSlotSelectorTags(BComponent device) {
		BOrd slotSelectorOrd = BOrd.make(device.getNavOrd() + "|bql:select from honApplicationHandler:HonWizSlotSelector");
		BITable<?> slotSelectors = (BITable<?>) slotSelectorOrd.resolve(device).get();

		Cursor<?> c = slotSelectors.cursor();
		Map<BHonWizSlotSelector, BComponent> itemsToBeRemoved = new HashMap<>();
		while (c.next()) {
			BHonWizSlotSelector slotSelector = (BHonWizSlotSelector) c.get();
			BComponent parentComponent = (BComponent) slotSelector.getParent();
			itemsToBeRemoved.put(slotSelector, parentComponent);
		}

		Iterator<Map.Entry<BHonWizSlotSelector, BComponent>> itemsToBeRemovedItr = itemsToBeRemoved.entrySet().iterator();
		while(itemsToBeRemovedItr.hasNext()) {
			Entry<BHonWizSlotSelector, BComponent> itemDetails = itemsToBeRemovedItr.next();
			BComponent parentComp = itemDetails.getValue();
			BHonWizSlotSelector selectorToBeRemoved = itemDetails.getKey();
			parentComp.remove(selectorToBeRemoved);
		}
	}
	
	public static void cleanUpExistingHonPointSelectorTags(BComponent device) {
		BOrd pointSelectorOrd = BOrd.make(device.getNavOrd() + "|bql:select from honApplicationHandler:HonWizPointSelector");
		BITable<?> pointSelectors = (BITable<?>) pointSelectorOrd.resolve(device).get();

		Cursor<?> c = pointSelectors.cursor();
		Map<BHonWizPointSelector, BComponent> itemsToBeRemoved = new HashMap<>();
		while (c.next()) {
			BHonWizPointSelector pointSelector = (BHonWizPointSelector) c.get();
			BComponent parentComponent = (BComponent) pointSelector.getParent();
			itemsToBeRemoved.put(pointSelector, parentComponent);
		}

		Iterator<Map.Entry<BHonWizPointSelector, BComponent>> itemsToBeRemovedItr = itemsToBeRemoved.entrySet().iterator();
		while(itemsToBeRemovedItr.hasNext()) {
			Entry<BHonWizPointSelector, BComponent> itemDetails = itemsToBeRemovedItr.next();
			BComponent parentComp = itemDetails.getValue();
			BHonWizPointSelector selectorToBeRemoved = itemDetails.getKey();
			parentComp.remove(selectorToBeRemoved);
		}

	}
    public static double getStep(BUnit unit, BComponent selector) {
        double step = 1;
        if(null != unit && unit.getUnitName().toLowerCase().contains(CELSIUS)){
            step = 0.5;
        } else {
            String stepValue = HoneywellConfigurableDeviceUtil.getStringTagValueOfComponent(selector, BHonWizardTag.STEP_TAG);
            step = stepValue == null || stepValue.trim().isEmpty() ? step : Double.valueOf(stepValue);

        }
        return step;
    }
    public static int getPrecision(BUnit unit, double step){
        //get decimal places number of step
        String stepStr = String.valueOf(step);
        int decimalPlaces = 0;
        if(stepStr.contains(".")) {
            decimalPlaces = stepStr.length() - stepStr.indexOf(".") - 1;
        }
        return decimalPlaces;
    }

    /**
     * get all hon wizard related slots, include rule store, global store, all selectors and all actions.
     * @param device, honeywell device
     * @return slot map
     */
    public static HashMap<Slot, BComplex> getHoneywellWizardSlots(BIHoneywellConfigurableDevice device) {
        BHonWizardGlobalStore globalStore = device.getGlobalStore();
        BHonWizardRuleStore ruleStore = device.getRuleStore();
        HashMap<Slot, BComplex> wizardRelatedSlots = new HashMap<>();
        if(null != globalStore) {
            BComplex parent = globalStore.getParent();
            wizardRelatedSlots.put(parent.getSlot(globalStore.getName()), parent);
            wizardRelatedSlots.put(BHonWizardGlobalStore.ruleQuery, globalStore);
        }
        if(null != ruleStore) {
            BComplex parent = ruleStore.getParent();
            wizardRelatedSlots.put(parent.getSlot(ruleStore.getName()), parent);
            wizardRelatedSlots.put(BHonWizardRuleStore.validateRuleConfig, ruleStore);
            wizardRelatedSlots.put(BHonWizardRuleStore.addValueRule, ruleStore);
            wizardRelatedSlots.put(BHonWizardRuleStore.addVisibilityRule, ruleStore);
            wizardRelatedSlots.put(BHonWizardRuleStore.addTerminalAssignmentValueRule, ruleStore);
        }
        if(null != device.getAttachHonWizardTagsAction()) {
            wizardRelatedSlots.put(device.getAttachHonWizardTagsAction(), (BComplex) device);
        }
        if(null != device.getGenerateGlobalStoreAction()) {
            wizardRelatedSlots.put(device.getGenerateGlobalStoreAction(), (BComplex) device);
        }
        if(null != device.getGenerateTagMappingFileAction()) {
            wizardRelatedSlots.put(device.getGenerateTagMappingFileAction(), (BComplex) device);
        }
        if(null != device.getValidateHonWizardTagsAction()) {
            wizardRelatedSlots.put(device.getValidateHonWizardTagsAction(), (BComplex) device);
        }
        List<BHonWizSelector> selectors = HoneywellConfigurableDeviceUtil.getAllHonWizardSelectors((BComponent) device);
        for(BHonWizSelector selector : selectors) {
            BComplex selectorParent = selector.getParent();
            Slot selectorSlot = selectorParent.getSlot(selector.getName());
            wizardRelatedSlots.put(selectorSlot, selectorParent);

        }
        return wizardRelatedSlots;

    }

    /**
     * get valid niagara name for slot path.
     * @param name, input name
     * @return valid name
     */
    public static String getValidNiagaraName(String name){
        return SlotPath.isValidName(name)?name:SlotPath.escape(name);
    }

	public static void createDuctareacalcSlotSelectorAndUpdateCopy(BIHoneywellConfigurableDevice device, String metaDataSlotPath,
			BDuctAreaCalculator areaRoleWidgetComponent, String role, BDuctAreaCalculator newRoleWidgetComponent) {
		BComponent selector = areaRoleWidgetComponent.getAssocaitedSelector();
		BComponent newAssociatedSelector = (BComponent) selector.newCopy();
		BHonWizSlotSelector newSlotSelector = new BHonWizSlotSelector();
		for (Property prop : selector.getDynamicPropertiesArray()) {
			newSlotSelector.add(prop.getName(), selector.get(prop), Flags.METADATA);
		}
		String metaDataPropName = DUCT_AREA_CALCULATOR_TYPE + "_" + role;
		String fullSlotPath = metaDataSlotPath + "/" + metaDataPropName;
		newRoleWidgetComponent.setCommonSlot(BOrd.make(fullSlotPath));


		// Remove options by default. Re-add as required.
		if (null != newAssociatedSelector.get(SlotPath.escape(BHonWizardTag.WIZ_OPTIONS_TAG.toString()))) {
			newAssociatedSelector.remove(SlotPath.escape(BHonWizardTag.WIZ_OPTIONS_TAG.toString()));
		}

		newSlotSelector.tags().set(BHonWizardTag.ROLE_TAG, BString.make(role));
		if (null != newRoleWidgetComponent.getOptionDetails()) {
			newSlotSelector.tags().set(BHonWizardTag.WIZ_OPTIONS_TAG, newRoleWidgetComponent.getOptionDetails());
			newAssociatedSelector.add(SlotPath.escape(BHonWizardTag.WIZ_OPTIONS_TAG.toString()), newRoleWidgetComponent.getOptionDetails(), Flags.METADATA);
		}

		// Update the Name tag to the proper name
		newSlotSelector.tags().set(BHonWizardTag.NAME_TAG, BString.make(newRoleWidgetComponent.getLabel()));

		if (null == device.getMetaDataComp().get(metaDataPropName)) {
			device.getMetaDataComp().add(metaDataPropName, newRoleWidgetComponent.getDefaultValueBasedOnRole());
			if (null != newRoleWidgetComponent.getEnumRange()) {
				device.getMetaDataComp().setFacets(device.getMetaDataComp().getSlot(metaDataPropName),
						BFacets.make(BFacets.RANGE, newRoleWidgetComponent.getEnumRange()));
			}
		} else {
			// Update the value back to GlobalStore.
			BValue currentData = device.getMetaDataComp().get(metaDataPropName);

			if(currentData instanceof BEnum) {
				newRoleWidgetComponent.setValue(String.valueOf(((BEnum) currentData).getOrdinal()));
			} else {
				newRoleWidgetComponent.setValue(currentData.toString());
			}
		}
		device.getMetaDataComp().add("honWizSlotSelector?", newSlotSelector);
		if (null != newAssociatedSelector.get(SlotPath.escape(BHonWizardTag.ROLE_TAG.toString()))) {
			newAssociatedSelector.set(SlotPath.escape(BHonWizardTag.ROLE_TAG.toString()), BString.make(role));
		}
        HoneywellWidgetTagUtil.setSourcePropertyName(DUCT_AREA_CALCULATOR_TYPE, newSlotSelector, (BComponent) newSlotSelector.getParent(), metaDataPropName);
		newRoleWidgetComponent.setAssocaitedSelector(newAssociatedSelector);
	}
	
	
	public static BIHoneywellConfigurableDevice getDevice(BComplex p ) {
        while(p != null && !(p instanceof BStation)) {
            if (p instanceof BIHoneywellConfigurableDevice) {
                return (BIHoneywellConfigurableDevice) p;
            }
            p = p.getParent();
        }

        return null;
    }

    /**
     * find selector according to widget component ord
     * for slot selector, common slot is there in widget component, we will get source component according to the common slot
     * for point selector, we will loop through all hon point selector, then get its parent component, parent component is honwizardpoint
     * @param device
     * @param selectorMap
     * @param widgetComp
     * @return
     */
    public static BHonWizSelector findSelectorByWidgetCompOrd(BIHoneywellConfigurableDevice device, Map<String, BHonWizSelector> selectorMap, BDynamicWidgetComponentBase widgetComp){
        if(null == widgetComp){
            return null;
        }
        BHonWizSelector foundSelector = null;
        if(widgetComp.getCommonSlot() != null && !widgetComp.getCommonSlot().isNull()) {
            BOrd commonSlot = widgetComp.getCommonSlot();
            int pos = commonSlot.toString().lastIndexOf("/");
            String parentSlotPath = commonSlot.toString().substring(0, pos);
            String srcPropName = commonSlot.toString().substring(pos+1);
            try{
                BComponent parentComp = (BComponent) BOrd.make(parentSlotPath).resolve((BComponent) device).get();
                BHonWizSlotSelector[] children = parentComp.getChildren(BHonWizSlotSelector.class);
                for(int i = 0; i < children.length; i++) {
                    if(children[i].getSourcePropertyNameString().equals(srcPropName)){
                        foundSelector = children[i];
                        break;
                    }
                }
            }catch (Exception e){
                return null;
            }

        } else {
            return selectorMap.get(widgetComp.getHandleOrd().toString());
        }
        return foundSelector;
    }

    /**
     * get enum range for BEnum type, if BEnum is null, then get range from parent component property facets.
     * @param parentComp
     * @param propName
     * @param value
     * @return
     */
    public static BEnumRange getEnumRange(BComponent parentComp, String propName, BValue value){
        if(value instanceof BEnum){
            BEnumRange range = ((BEnum) value).getRange();
            if(range.isNull()){
                BObject bObject = parentComp.getProperty(propName).getFacets().get("range");
                if(bObject instanceof BEnumRange) {
                    return (BEnumRange) bObject;
                }
            }else{
                return range;
            }
        }
        return BEnumRange.DEFAULT;
    }

    /**
     * get unit according to selector, if it is slot selector, need to find slot by src property name
     * @param selector, selector
     * @param srcPropertyName, source property name
     * @return unit
     */

    public static BUnit getUnitBySelector(BHonWizSelector selector, String srcPropertyName) {
        BUnit unit = null;
        BComponent parent = (BComponent) selector.getParent();
        if(parent instanceof BIHonWizardPoint) {
            BIHonWizardPoint honWizardPoint = (BIHonWizardPoint) parent;
            unit = ((BIHonWizardNumericPoint) honWizardPoint).getUnit();
        }else {
            if(!srcPropertyName.isEmpty() && parent.getProperty(srcPropertyName) != null
                    && parent.getProperty(srcPropertyName).getFacets().get(BFacets.UNITS) != null) {
                unit = (BUnit) parent.getProperty(srcPropertyName).getFacets().get(BFacets.UNITS);
            }
        }
        return unit;
    }


}
