/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.ontology;

import javax.baja.sys.Sys;

public class Const {

    public static final String HON_WIZARD_TAG_NAMESPACE = "HonWiz";
    public static final int MAX_PAGE_ORDER = 16;
    public static final String NAMING_CONVENTION_HEADER = "NamingConventions";
    public static final String SLOT_PATH_HEADER = "RelativeSlotPath";
    public static final String WIDGET_TYPE_HEADER = "WidgetTypeTag";
    public static final String WIDGET_TYPE_TAG= "WidgetType";
    public static final String PAGE_TAG = "Page";
    public static final String PAGE_HEADER = "PageTag";
    public static final String ROLE_HEADER = "RoleTag";


    public static final String SCHEDULE_TYPE = "Schedule";
    public static final String TAG_MAPPING_FILE_PATH = "module://honApplicationHandler/TagMapping.xlsx";
    public static final String TAG_MAPPING_TEMPLATE_FILE_PATH = "module://honApplicationHandler/TagMappingTemplate.xlsx";

    public static final String TEMPLATE_FILE_PATH = Sys.getStationHome().toPath() + "/userConfigData/TagMappingTemplate.xlsx";
    public static final String BOOLEAN_TAG_TRUE = "Yes";
    public static final String BOOLEAN_TAG_FALSE = "No";
    public static final String STATION_ORD_BASE = "station:|";
    public static final String IGNORE_WIZARD_CONFIGURATION_UPDATE = "ignoreWizardConfigurationUpdate";

    public static final String TERMINAL_ASSIGNMENT_PAGE = "Terminal Assignment";

    public static final String HIGH_PRECISION_VALUE = "highPrecisionValue";
    public static final String HIGH_PRECISION_MIN = "highPrecisionMin";
    public static final String HIGH_PRECISION_MAX = "highPrecisionMax";
    public static final String HIGH_PRECISION_DEFAULT_VALUE = "highPrecisionDefaultValue";

    public static final String HIGH_PRECISION_DEAD_BAND = "highPrecisionDeadband";

    public static final String UNIT_NAME = "unitName";

    public static final String NAME = "name";
    public static final String UNIT = "unit";

    public static final String IMPERIAL = "imperial";

    public static final String ENGLISH = "english";

    public static final String METRIC = "metric";

    public static final String MAX = "max";

    public static final String MIN = "min";

    public static final String DEFAULT_VALUE = "defaultValue";

    public static final String VALUE = "value";

    public static final String INVALID_UNIT_CONVERSION_VALUE = "";

    public static final String STEP = "step";

    public static final String PRECISION = "precision";
    public static final String UNIT_GROUP = "unitGroup";

    public static final String CELSIUS = "celsius";
    public static final String FAHRENHEIT = "fahrenheit";

    public static final String DEAD_BAND = "deadband";

    public static final int DEFAULT_PRECISION = 0;

    public static final String UNASSIGNED = "unassigned";

    public static final String HON_WIZARD_CATEGORY = "honWizardCategory";
    public static final String HON_DEVICE_CATEGORY = "honDeviceCategory";

    public static final String SEPARATOR_LABEL_ROLE_CHARACTER = "-";
    
    public static final String FORCE_RESUME_DYNAMIC_UPDATE = "forceStartDynamicUpdate";

}
