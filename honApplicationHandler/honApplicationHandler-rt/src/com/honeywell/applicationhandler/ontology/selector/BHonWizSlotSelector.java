/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any
 *  purpose without the express written permission of Honeywell.
 */
package com.honeywell.applicationhandler.ontology.selector;

import com.honeywell.applicationhandler.utils.HoneywellWidgetTagUtil;
import com.honeywell.applicationhandler.widgetcomponents.BDynamicWidgetComponentBase;

import javax.baja.bql.BqlQuery;
import javax.baja.collection.BITable;
import javax.baja.data.BIDataValue;
import javax.baja.naming.OrdTarget;
import javax.baja.naming.SlotPath;
import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BComponent;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BValue;
import javax.baja.sys.Context;
import javax.baja.sys.Cursor;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;


@NiagaraProperty(
        name = "sourcePropertyName",
        type = "baja:Enum",
        defaultValue = "BDynamicEnum.DEFAULT"
)
@NiagaraType
@SuppressWarnings({
        "squid:MaximumInheritanceDepth",
        "squid:S2160",
        "squid:S1213"  // slot-o-matic has its own order of members and methods
})
public class BHonWizSlotSelector extends BHonWizSelector {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ontology.selector.BHonWizSlotSelector(2352485181)1.0$ @*/
/* Generated Mon Jul 07 14:04:45 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "sourcePropertyName"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code sourcePropertyName} property.
   * @see #getSourcePropertyName
   * @see #setSourcePropertyName
   */
  public static final Property sourcePropertyName = newProperty(0, BDynamicEnum.DEFAULT, null);
  
  /**
   * Get the {@code sourcePropertyName} property.
   * @see #sourcePropertyName
   */
  public BEnum getSourcePropertyName() { return (BEnum)get(sourcePropertyName); }
  
  /**
   * Set the {@code sourcePropertyName} property.
   * @see #sourcePropertyName
   */
  public void setSourcePropertyName(BEnum v) { set(sourcePropertyName, v, null); }

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizSlotSelector.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

    @Override
    protected BDynamicWidgetComponentBase getWidgetComponent() {
        String sourcePropertyNameString = getSourcePropertyNameString();
        if(sourcePropertyNameString.isEmpty()) {
            return null;
        }
        BComponent device = (BComponent) selectorDevice;
        if(device == null) {
            return null;
        }

        String query = "select * from honApplicationHandler:DynamicWidgetComponentBase where commonSlot != null";
        OrdTarget target = OrdTarget.unmounted(device);

        BqlQuery bqlQuery = BqlQuery.make(query);
        BITable<BDynamicWidgetComponentBase> result = (BITable<BDynamicWidgetComponentBase>) bqlQuery.resolve(target).get();

        SlotPath parentSlotPath = selectorParent.getSlotPath();
        String sourceSlotPath = parentSlotPath.toString() + '/' + sourcePropertyNameString;

        Cursor<BDynamicWidgetComponentBase> cursor = result.cursor();
        while(cursor.next()) {
            BDynamicWidgetComponentBase component = cursor.get();
            if(component.getCommonSlot().encodeToString().equals(sourceSlotPath)) {
                return component;
            }
        }

        return null;
    }
    
    @Override
    protected BEnumRange getTargetEnumRange() {
        String sourcePropertyNameString = getSourcePropertyNameString();
        BValue sourcePropertyValue = selectorParent.get(sourcePropertyNameString);
        if(sourcePropertyNameString.isEmpty() || !(sourcePropertyValue instanceof BEnumRange)) {
            return null;
        }

        return (BEnumRange) sourcePropertyValue;
    }

    @Override
    public void started() {
        super.started();
        HoneywellWidgetTagUtil.checkAndSetSourcePropertyName(this);

    }
    @Override
    public void changed(Property property, Context context) {
        BIDataValue widgetTypeTagValue = checkAndGetWidgetTypeTagValue(property);
        if(null != widgetTypeTagValue) {
            HoneywellWidgetTagUtil.setSourcePropertyName(widgetTypeTagValue.toString(), this,
                    (BComponent) this.getParent(), this.getSourcePropertyNameString());
        }
        super.changed(property, context);
    }

    public  String getSourcePropertyNameString(){
        if(SlotPath.isValidName(getSourcePropertyName().toString())){
            return getSourcePropertyName().toString();
        }
        return SlotPath.escape(getSourcePropertyName().toString());

    }
}
