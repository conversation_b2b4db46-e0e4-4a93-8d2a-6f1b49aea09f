/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.ontology;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.baja.nre.annotations.NiagaraProperty;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BBoolean;
import javax.baja.sys.BDynamicEnum;
import javax.baja.sys.BEnumRange;
import javax.baja.sys.BString;
import javax.baja.sys.Flags;
import javax.baja.sys.Property;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.tag.Id;
import javax.baja.tagdictionary.BSimpleTagInfo;
import javax.baja.tagdictionary.BTagDictionary;

import com.honeywell.applicationhandler.enums.BUnitGroupEnum;
import static com.honeywell.applicationhandler.common.Constants.WIDGET_TYPE_LIST;

@NiagaraProperty(
        name = "namespace",
        type = "String",
        defaultValue = "Const.HON_WIZARD_TAG_NAMESPACE",
        flags = Flags.READONLY,
        override = true
)
@NiagaraType
public class BHonWizardTag extends BTagDictionary {
/*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
/*@ $com.honeywell.applicationhandler.ontology.BHonWizardTag(354033400)1.0$ @*/
/* Generated Wed Mar 12 15:33:46 CST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012 */

////////////////////////////////////////////////////////////////
// Property "namespace"
////////////////////////////////////////////////////////////////
  
  /**
   * Slot for the {@code namespace} property.
   * @see #getNamespace
   * @see #setNamespace
   */
  public static final Property namespace = newProperty(Flags.READONLY, Const.HON_WIZARD_TAG_NAMESPACE, null);

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////
  
  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BHonWizardTag.class);

/*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
  
  
    public BHonWizardTag() {
	    BString s = BString.make("");
        BEnumRange widgetTypeEnumRange = BEnumRange.make(WIDGET_TYPE_LIST);
        BDynamicEnum widgetTypes = BDynamicEnum.make(widgetTypeEnumRange);
	    getTagDefinitions().add(HON_WIZ_PAGE_TAG, new BSimpleTagInfo(s));
	    getTagDefinitions().add(HON_WIZ_GRP_TAG, new BSimpleTagInfo(s));
	    getTagDefinitions().add(HON_WIZ_NAME_TAG, new BSimpleTagInfo(s));
	    getTagDefinitions().add(HON_WIZ_WDTTYPE_TAG, new BSimpleTagInfo(widgetTypes));
        getTagDefinitions().add(HON_WIZ_ROLE_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_BELONG_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_COLOR_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_TAB_IN_PAGE_TAG, new BSimpleTagInfo(s));
	    getTagDefinitions().add(HON_WIZ_DEF_VAL_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_READ_ONLY_TAG, new BSimpleTagInfo(BBoolean.FALSE));
        getTagDefinitions().add(HON_WIZ_MIN_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_MAX_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_DEADBAND_TAG, new BSimpleTagInfo(s));
	    getTagDefinitions().add(HON_WIZ_ORDER_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_OPTIONS_TAG, new BSimpleTagInfo(BEnumRange.DEFAULT));
        getTagDefinitions().add(HON_WIZ_HELP_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_STEP_TAG, new BSimpleTagInfo(s));
        getTagDefinitions().add(HON_WIZ_UNIT_GROUP_TAG, new BSimpleTagInfo(BUnitGroupEnum.DEFAULT));
        for (int i = 0; i < Const.MAX_PAGE_ORDER; i++) {
            getTagDefinitions().add(HON_WIZ_PAGE_ORDER + i, new BSimpleTagInfo(s));
        }
	    setEnabled(true);
    }
    public static final String HON_WIZ_PAGE_TAG = "Page";
    public static final String HON_WIZ_GRP_TAG = "Group";
    public static final String HON_WIZ_NAME_TAG = "Name";
    public static final String HON_WIZ_WDTTYPE_TAG = "WidgetType";
    public static final String HON_WIZ_UNIT_GROUP_TAG = "UnitGroup";
    public static final String HON_WIZ_STEP_TAG = "Step";
    public static final String HON_WIZ_HELP_TAG = "Help";
    public static final String HON_WIZ_ROLE_TAG = "Role";
    public static final String HON_WIZ_BELONG_TAG = "Belong";
    public static final String HON_WIZ_COLOR_TAG = "Color";
    public static final String HON_WIZ_TAB_IN_PAGE_TAG = "TabInPage";
    public static final String HON_WIZ_DEF_VAL_TAG = "DefaultValue";
    public static final String HON_WIZ_READ_ONLY_TAG = "ReadOnly";
    public static final String HON_WIZ_MIN_TAG = "Min";
    public static final String HON_WIZ_MAX_TAG = "Max";
    public static final String HON_WIZ_DEADBAND_TAG = "Deadband";
    public static final String HON_WIZ_ORDER_TAG = "Order";
    public static final String HON_WIZ_OPTIONS_TAG = "Options";
    public static final String HON_WIZ_PAGE_ORDER = "PageOrder_";

    public static final List<String> ENUM_RANGE_TAGS = new ArrayList<>();
    public static final List<String> BOOLEAN_TAGS = new ArrayList<>();
    public static final Map<String, String> TAG_NAME_TO_PROPERTY_MAP = new HashMap<>();


    public static final Id PAGE_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_PAGE_TAG);
    public static final Id GRP_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_GRP_TAG);
    public static final Id NAME_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_NAME_TAG);
    public static final Id WGTTYPE_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_WDTTYPE_TAG);
    public static final Id UNITGRP_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_UNIT_GROUP_TAG);

    public static final Id STEP_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_STEP_TAG);

    public static final Id HELP_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_HELP_TAG);
    public static final Id ROLE_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_ROLE_TAG);
    public static final Id BELONG_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_BELONG_TAG);
    public static final Id COLOR_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_COLOR_TAG);
    public static final Id TAB_IN_PAGE_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_TAB_IN_PAGE_TAG);
    public static final Id DEF_VAL_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_DEF_VAL_TAG);
    public static final Id READ_ONLY_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_READ_ONLY_TAG);
    public static final Id MIN_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_MIN_TAG);
    public static final Id MAX_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_MAX_TAG);
    public static final Id DEADBAND_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_DEADBAND_TAG);
    public static final Id WIZ_ORDER_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_ORDER_TAG);
    public static final Id WIZ_OPTIONS_TAG = Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_OPTIONS_TAG);
    public static final List<Id> WIZ_PAGE_ORDER = new ArrayList<>();

    static {
        for (int i = 0; i < Const.MAX_PAGE_ORDER; i++) {
            WIZ_PAGE_ORDER.add(Id.newId(Const.HON_WIZARD_TAG_NAMESPACE + ":" + HON_WIZ_PAGE_ORDER + i));
        }

        ENUM_RANGE_TAGS.add(HON_WIZ_OPTIONS_TAG);
        ENUM_RANGE_TAGS.add(HON_WIZ_WDTTYPE_TAG);
        ENUM_RANGE_TAGS.add(HON_WIZ_UNIT_GROUP_TAG);

        BOOLEAN_TAGS.add(HON_WIZ_READ_ONLY_TAG);

        TAG_NAME_TO_PROPERTY_MAP.put(NAME_TAG.getQName(), "label");
        TAG_NAME_TO_PROPERTY_MAP.put(WGTTYPE_TAG.getQName(), "componentType");
        TAG_NAME_TO_PROPERTY_MAP.put(STEP_TAG.getQName(), "step");
        TAG_NAME_TO_PROPERTY_MAP.put(HELP_TAG.getQName(), "help");
        TAG_NAME_TO_PROPERTY_MAP.put(ROLE_TAG.getQName(), "role");
        TAG_NAME_TO_PROPERTY_MAP.put(BELONG_TAG.getQName(), "belong");
        TAG_NAME_TO_PROPERTY_MAP.put(COLOR_TAG.getQName(), "color");
        TAG_NAME_TO_PROPERTY_MAP.put(TAB_IN_PAGE_TAG.getQName(), "tabInPage");
        TAG_NAME_TO_PROPERTY_MAP.put(READ_ONLY_TAG.getQName(), "readOnly");
        TAG_NAME_TO_PROPERTY_MAP.put(MIN_TAG.getQName(), "min");
        TAG_NAME_TO_PROPERTY_MAP.put(MAX_TAG.getQName(), "max");
        TAG_NAME_TO_PROPERTY_MAP.put(DEADBAND_TAG.getQName(), "deadband");
        TAG_NAME_TO_PROPERTY_MAP.put(WIZ_OPTIONS_TAG.getQName(), "options");
        TAG_NAME_TO_PROPERTY_MAP.put(UNITGRP_TAG.getQName(), "unitGroup");
    }
}
