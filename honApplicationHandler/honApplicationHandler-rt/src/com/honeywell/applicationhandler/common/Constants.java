/*
 *  Copyright (c) 2025 Honeywell International, Inc. All rights reserved.
 *
 *  This file contains trade secrets of Honeywell International, Inc.  No part
 *  may be reproduced or transmitted in any form by any means or for any
 *  purpose without the express written permission of Honeywell.
 */

package com.honeywell.applicationhandler.common;

public final class Constants {
    private Constants(){

    }
    public static final String STATION_PATH="station:|h:";
    public static final String MYJSON="myJSON";
    public static final String DEVICE_HANDLE ="deviceHandle";
    public static final String VALUE_OBJECT = "valueObject";


    public static final String SVG = "svg";

    public static final String PIN_NAME = "pinName";

    public static final String FB_NAME = "fbName";

    public static final String TERMINAL_ASSIGNMENT = "Terminal$20Assignment";
    public static final String TIMEZONE_TYPE = "Timezone";
    public static final String DUCT_AREA_CALCULATOR_TYPE = "DuctAreaCalculator";
    public static final String SELECT_WIDGET = "SelectWidget";

    public static final String MEASUREMENT_TYPE = "MeasurementType";
    public static final String CHECKBOX_GROUP = "CheckboxGroup";
    public static final String SINGLE_SLIDER = "SingleSlider";
    public static final String RANGE_SLIDER = "RangeSlider";
    public static final String NUMBER_INPUT = "NumberInput";
    public static final String TEXT_INPUT = "TextInput";
    public static final String DROPDOWN = "Dropdown";
    public static final String SWITCH_BUTTON = "SwitchButton";
    public static final String RADIO_BUTTON_GROUP = "RadioButtonGroup";
    public static final String SELECT_BUTTON = "SelectButton";
    public static final String SCHEDULE = "Schedule";
    public static final String HEADING_LABEL = "HeadingLabel";
    public static final String AIRFLOW_UNIT = "AirflowUnit";
    public static final String[] WIDGET_TYPE_LIST = new String[]{MEASUREMENT_TYPE, AIRFLOW_UNIT, TIMEZONE_TYPE, DROPDOWN,
            DUCT_AREA_CALCULATOR_TYPE, NUMBER_INPUT, RANGE_SLIDER, SINGLE_SLIDER, SELECT_WIDGET, SWITCH_BUTTON, TEXT_INPUT, SCHEDULE};
    public static final String REQUIRED = "-R";
    public static final String OPTIONAL = "-O";

    public static final String SOURCE_PROPERTY_NAME = "sourcePropertyName";
    public static final String HON_WIZ_POINT_SELECTOR = "honWizPointSelector?";
    public static final String HON_WIZ_SLOT_SELECTOR = "honWizSlotSelector?";

    public static final String NO_VALID_SLOTS_AVAILABLE = "NoValidSlotsAvailable";
}
