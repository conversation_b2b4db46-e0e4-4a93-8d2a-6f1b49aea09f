# BHonWizardGlobalStore 测试类说明

## 概述

`BHonWizardGlobalStoreTest` 是为 `BHonWizardGlobalStore` 类编写的全面单元测试类，用于验证全局存储组件的核心功能。

## 测试覆盖范围

### 核心功能测试
- **基础功能测试** (`testBasicFunctionality`): 验证组件创建和基本操作
- **页面顺序管理** (`testPageOrderManagement`): 测试页面顺序的增删改查操作
- **组件值保存** (`testSaveComponentValue`): 验证组件值的保存功能
- **组件检索** (`testGetWidgetComponent`): 测试组件的获取功能

### 业务逻辑测试
- **调度详情保存** (`testSaveScheduleDetails`): 验证调度信息的保存
- **规则查询** (`testRuleQuery`): 测试规则查询功能
- **终端分配更新** (`testUpdateTerminalAssignments`): 验证终端分配更新

### 数据处理测试
- **数据编码** (`testDataEncoding`): 测试向导数据的编码功能
- **数据解码** (`testDataDecoding`): 测试向导数据的解码功能
- **标签处理** (`testTagHandling`): 验证标签数据的处理

### 生命周期测试
- **组件生命周期** (`testLifecycleMethods`): 测试 started() 和 descendantsStarted() 方法
- **属性变更处理** (`testPropertyChangeHandling`): 验证属性变更的处理逻辑

### 高级测试
- **集成场景** (`testIntegrationScenarios`): 测试多个操作的组合场景
- **并发访问** (`testConcurrentAccess`): 基础的线程安全验证
- **性能特性** (`testPerformanceCharacteristics`): 关键操作的性能测试
- **输入验证** (`testInputValidation`): 参数验证测试
- **状态一致性** (`testStateConsistency`): 状态一致性验证
- **错误处理** (`testErrorHandling`): 异常情况和边界条件测试

## 测试分组

测试使用 TestNG 的分组功能进行组织：

- `core`: 核心功能测试
- `pageOrder`: 页面顺序相关测试
- `widgets`: 组件相关测试
- `schedule`: 调度相关测试
- `rules`: 规则相关测试
- `encoding`: 数据编码测试
- `decoding`: 数据解码测试
- `tags`: 标签处理测试
- `lifecycle`: 生命周期测试
- `properties`: 属性处理测试
- `fileGeneration`: 文件生成测试
- `terminalAssignment`: 终端分配测试
- `integration`: 集成测试
- `concurrency`: 并发测试
- `performance`: 性能测试
- `validation`: 验证测试
- `stateConsistency`: 状态一致性测试
- `errorHandling`: 错误处理测试

## 运行测试

### 运行所有测试
```bash
gradlew :honApplicationHandler-rt:moduleTestJar
gradlew test honApplicationHandler:HonWizardGlobalStoreTest
```

### 运行特定分组的测试
```bash
# 运行核心功能测试
gradlew test honApplicationHandler:HonWizardGlobalStoreTest -Dgroups=core

# 运行集成测试
gradlew test honApplicationHandler:HonWizardGlobalStoreTest -Dgroups=integration

# 运行性能测试
gradlew test honApplicationHandler:HonWizardGlobalStoreTest -Dgroups=performance
```

### 生成测试覆盖率报告
```bash
gradlew :honApplicationHandler-rt:jacocoNiagaraTestReport
```

## 测试设计原则

1. **隔离性**: 每个测试方法都是独立的，使用 `@BeforeMethod` 和 `@AfterMethod` 进行设置和清理
2. **可重复性**: 测试可以多次运行并产生相同的结果
3. **全面性**: 覆盖正常流程、边界条件和异常情况
4. **可维护性**: 使用清晰的命名和充分的注释
5. **性能意识**: 包含基本的性能验证测试

## 注意事项

1. **Mock 对象**: 测试中使用了 Mock 对象来模拟依赖组件，避免对外部系统的依赖
2. **异常处理**: 大部分测试使用 `assertDoesNotThrow` 来验证方法不会抛出异常
3. **日志记录**: 每个测试方法都包含适当的日志记录，便于调试
4. **资源清理**: 在 `tearDown` 方法中进行适当的资源清理

## 扩展建议

为了进一步提高测试覆盖率，建议添加以下测试：

1. **更详细的Mock测试**: 使用 Mockito 等框架创建更精确的 Mock 对象
2. **数据驱动测试**: 使用 `@DataProvider` 进行参数化测试
3. **集成测试**: 与实际的 Niagara 站点环境进行集成测试
4. **压力测试**: 大数据量和高并发场景的测试
5. **回归测试**: 针对已知问题的回归测试用例

## 依赖关系

测试类依赖以下组件：
- Niagara 4 Framework
- TestNG 测试框架
- BHonWizardGlobalStore 及其相关类
- 各种 Widget 组件类
- 标签字典服务

确保在运行测试前，所有依赖都已正确配置。
