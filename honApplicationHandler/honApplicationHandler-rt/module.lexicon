#
# Lexicon for the honApp<PERSON>Handler module.
#


WIZARD_JOB_STARTED = The "{0}" job successfully started for device: "{1}"
honWizardJob.ruleParser = Generate and Save Rule Configurations
honWizardJob.validateRule = Validate Rules
honWizard.RuleParse.Error = Error while parsing the rule : "{0}" for device "{1}"
honWizardJob.cancelled = The {0} job for device {1} has been stopped by the user.
BHonWizardRuleParserJob.writeFailed = Error occurred while processing rules for device "{0}" : "{1}"
BHonWizardRuleParserJob.fail = Rule config update for device "{0}" could not be completed due to syntax errors in defined rules. Please review the job log details and try again.
BHonWizardRuleParserJob.success= Rule config has been successfully updated for device "{0}"
BHonWizardRuleParserJob.norules= No rules are available to be updated for device "{0}"

honWizardJob.saveJob = Wizard Value Update
BHonWizardValueSaveJob.terminalPageNotFound = Terminal assignment page component not found
BHonWizardValueSaveJob.terminalAssignmentError = Error occurred while updating terminal assignment for device {0}
BHonWizardValueSaveJob.fail = Wizard value update for device "{0}" could not be completed. Please review the job log details and try again.
BHonWizardValueSaveJob.success= All the values has been successfully updated for device "{0}"

honWizardJob.tagFileGenJob = Generate File from Tags
BHonWizardTagMappingFileGenJob.fail = File generation from tag for device "{0}" could not be completed. Please review the job log details and try again.
BHonWizardTagMappingFileGenJob.success= File has been generated successfully for device "{0}" and is available in "{1}".

honWizardJob.globalStoreGenJob = Generate Wizard Configuration from Tags
BHonWizardGlobalStoreGenJob.validation.start = Start to validate tags
BHonWizardGlobalStoreGenJob.fail = Wizard config generation from tag for device "{0}" could not be completed. Please review the job log details and try again.
BHonWizardGlobalStoreGenJob.success= Wizard configurations has been successfully generated for device "{0}"
HonWizGlobalStoreGenerator.exception.occurred = Exception occurred while generating wizard configurations : {0}
HonWizGlobalStoreGenerator.BHonWizSlotSelector.found = BHonWizSlotSelector found in {0}
HonWizGlobalStoreGenerator.property.notfound = {0} is not found in {1}
HonWizGlobalStoreGenerator.exception.minmax = Exception Occurred While deriving Min/Max Tag
HonWizGlobalStoreGenerator.generate.checkbox.failed = failed to generate checkBox for {0}
HonWizGlobalStoreGenerator.generate.checkbox.failed.forPoint = failed to generate checkBox
HonWizGlobalStoreGenerator.slottype.notsupported = Slot type is not supported to generate {0} for {1}
HonWizGlobalStoreGenerator.tagvalue.invalid = Invalid {0} tag value for {1}
HonWizGlobalStoreGenerator.point.notsupported = This point ({0}) is not supported to be shown in Wizard. Point should be instance of BIHonWizardPoint
HonWizGlobalStoreGenerator.generate.failed = Unable to create {0} for {1}
HonWizGlobalStoreGenerator.multi.schedule=More than 1 schedule has been attached with same tab: {0}. Please assign schedules to different tabs.

honWizardJob.tagGenJob = Generate HonWizardTag from TagMapping file
BHonWizardTagGenJob.loadingTagMapping = Loading TagMapping file
BHonWizardTagGenJob.validation.start = Start to validate TagMappings
BHonWizardTagGenJob.success = HonWizardTag has been successfully attached for device "{0}"
BHonWizardTagGenJob.fail = HonWizardTag attaching from TagMapping excel file for device "{0}" could not be completed. Please review the job log details and try again.

honWizardJob.tagValidationJob = Validate Honeywell Wizard Tags
BHonWizardTagValidationJob.validation.start = Start to validate tags
BHonWizardTagValidationJob.fail = HonWizard Tags Validation for device "{0}" could not be completed. Please review the job log details and try again.
BHonWizardTagValidationJob.success= HonWizard Tags Validation for device "{0}" is successful

error.collecting.tags.slotselector=Error collecting tags from BHonWizSlotSelector
error.collecting.tags.pointselector=Error collecting tags from BHonWizPointSelector
warning.tag.not.found=Tag not found
error.creating.excel.file=Error creating Excel file
error.invalid.column.name=Invalid column name
error.tagdetail.update=Error occurred while updated file with data: "{0}" : "{1}"
HoneywellDeviceWizard.terminalAssignment.svgNotFound=Terminal Assignment SVG file not found {0}.
HoneywellDeviceWizard.terminalAssignment.parseSvgError=Parse SVG file error.
HoneywellDeviceWizard.UnitConversionUtil.parseUnit=Can not parse unit from unit name {0}.
HoneywellDeviceWizard.UnitConversionUtil.parseMeasurementType=Can not parse measurement type {0}.
HoneywellDeviceWizard.UnitConversionUtil.desiredUnitNotFound= desired unit not found, unit name {0}.measurement type {1}.
HoneywellDeviceWizard.UnitConversionUtil.desiredUnitWithSameDimensionNotFoundInUnitConversion=desired unit with same dimension not found, unit name {0}.measurement type {1}.
HoneywellDeviceWizard.UnitConversionUtil.highPrecisionValueNotNumeric=High precision value is not numeric, property name:{0}, value {1}.
HoneywellDeviceWizard.UnitConversionUtil.minNotNumeric=Min value is not numeric, property name:{0}, value {1}.
HoneywellDeviceWizard.UnitConversionUtil.maxNotNumeric=Max value is not numeric, property name:{0}, value {1}.
HoneywellDeviceWizard.UnitConversionUtil.defaultValueNotNumeric=Default value is not numeric, property name:{0}, value {1}.
HoneywellDeviceWizard.UnitConversionUtil.deadBandNotNumeric=Dead band value is not numeric, property name:{0}, value {1}.

error.namingconvention.validation.point.notfound = No point found for naming convention: "{0}"
error.namingconvention.validation.point.notsingle = More than one point are found for naming convention: "{0}"
error.namingconvention.validation.point.found = Found points:
error.slotpath.validation.notfound = Can not resolve slot path: "{0}"
error.slotpath.validation.duplicated = Duplicated slot paths are found in the TagMapping excel file: "{0}"
error.tagvalidation.tagname = Tag Name(s):
error.tagvalidation.reason = Reason:
error.tagvalidation.pointselector.duplicated = Point Selector duplicated:
error.tagvalidation.slotselector.duplicated = Slot Selector duplicated:
error.tagvalidation.pointtype.invalid = Invalid point type:
error.tagvalidation.tagvalue.invalid = Invalid tag value:
error.tagvalidation.tag.notsupported = Not supported tag error:
error.tagvalidation.currentvalue = Current tag value:
error.tagvalidation.property.notexist = Property not exist:
error.tagvalidation.role.duplicated = Role tag duplicated:
error.tagvalidation.order.duplicated = Order tag duplicated:
error.tagvalidation.pagename.duplicated = Tab name duplicated:
error.tagvalidation.name.duplicated = Name tag duplicated:
error.tagvalidation.unit.notconsistency = Unit of range slider is not consistent:
error.tagvalidation.tag.notsync = Tag values not sync:
error.tagvalidation.point.missing = Point missing:
error.tagvalidation.binding.point = Binding points:
error.tagvalidation.binding.role = Binding roles:
error.tagvalidation.missing.role = Missing roles:
error.tagvalidation.duplicated.role = Duplicated role: {0}
error.tagvalidation.duplicated.order = Duplicated order: {0}
error.tagvalidation.duplicated.pagename = Duplicated tab: {0}
error.tagvalidation.duplicated.name = Duplicated name: {0}
error.tagvalidation.duplicated.role.pointselectors = Point selectors with duplicated role:
error.tagvalidation.duplicated.order.pointselectors = Point selectors with duplicated order:
error.tagvalidation.duplicated.pagename.pointselectors = Point selectors with duplicated tab names:
error.tagvalidation.duplicated.name.pointselectors = Point selectors with duplicated name:
error.tagvalidation.selector.slotpaths = Selector slot paths:
error.tagvalidation.tag.missing = Tag missing:
error.tagvalidation.missed.tag = Missed tag:
error.tagvalidation.notsupported.tag = Not supported tag(s):
error.tagvalidation.belong.tag.number.notcorrect = Belong tag number is not correct:
error.tagvalidation.belong.tagvalue = Belong tag value: {0}
error.tagvalidation.belong.samevalue.pointselectors = Point selectors with same Belong tag value:
error.tagvalidation.failed=Tag validation failed for device "{0}"
error.tagvalidation.reason.pointselector.duplicated = Only one BHonWizPointSelector is allowed for each point
error.tagvalidation.reason.slotselector.duplicated = There are more than 1 BHonWizSlotSelectors with same source property name "{0}"
error.tagvalidation.reason.commontag.required = Common tags are required for all points and slots
error.tagvalidation.reason.widgettype.validvalue = The valid widget type for this point or slot is "{0}"
error.tagvalidation.reason.string.type = {0} tag should be a string
error.tagvalidation.reason.string.length = {0} tag length should be not bigger than {1}
error.tagvalidation.reason.invalid.color = the color tag value should be a valid color name or a hex value like "#ffffff"
error.tagvalidation.reason.invalid.unitgroup = The unit group tag shall contain a valid unit group name, such as "Measurement Type", "Airflow Unit" or "None".
error.tagvalidation.reason.readonly.validtype = ReadOnly tag value should be boolean
error.tagvalidation.reason.numeric.invalid = {0} tag should be a number
error.tagvalidation.reason.positivenumeric.invalid = {0} tag should be a positive number and only two decimal places are allowed
error.tagvalidation.reason.step.invalid= {0} tag value should be less than (max-min)
error.tagvalidation.reason.deadband.exceed.upper.limit = Deadband tag value should be less than (max - min)
error.tagvalidation.reason.minmax.inpair = Min and Max tags should be in pair
error.tagvalidation.reason.minmax.compare = Min tag value should be less than Max tag value
error.tagvalidation.reason.minmax.outofrange = current value ({0}) is out of range [{1}, {2}]
error.tagvalidation.reason.order.invalid = Order tag value should be an integer number or an unsigned float number
error.tagvalidation.reason.name.invalid = Name tag value should not include {0} character
error.tagvalidation.reason.order.duplicated = Order tag value under same page tag value should not be the same
error.tagvalidation.reason.schedule.duplicated = All the schedules being configured should be assigned to unique tabs.
error.tagvalidation.reason.name.duplicated = Name tag value under same page tag value should not be the same
error.tagvalidation.reason.enumrange.type = {0} tag should be an EnumRange
error.tagvalidation.reason.options.ordinals = Options tag should have same ordinals with source point or slot
error.tagvalidation.reason.widget.tags.notsupported = Tags are not supported for widget type "{0}"
error.tagvalidation.reason.widget.requiredtags.missing = Tags are required for widget type "{0}"
error.tagvalidation.reason.widget.checkboxgroup.options = The key of option for checkboxgroup should be bitwise like 1, 2, 4, 8, 16, 32, 64 and so on
error.tagvalidation.reason.widget.rangeslider.belongtag.number.incorrect = There should be 2 points or slots with same Belong tag value for one RangeSlider
error.tagvalidation.reason.widget.rangeslider.roletag.invalidvalue = The value of Role tag for RangeSlider should be "min" or "max"
error.tagvalidation.reason.widget.rangeslider.roletag.missing = Role tag is missing for RangeSlider
error.tagvalidation.reason.widget.roletag.samevalue = Role tag value under same Belong tag value should not be the same
error.tagvalidation.reason.widget.rangeslider.tagnotsync = Other tag values except Role tag should be same between the 2 points or slots
error.tagvalidation.reason.widget.ductareacalculator.roletag.invalidvalue = The value of Role tag for DuctAreaCalculator should be "area"/"width"/"height"/"radius"/"ducttype"/"dimensionunit"
error.tagvalidation.reason.widget.ductareacalculator.belongtag.number.incorrect = There should be no more than 6 points or slots with same Belong tag value for one DuctAreaCalculator
error.tagvalidation.reason.widget.rangeslider.unitnotconsistency = the unit of 2 points or slots should be same
error.tagvalidation.reason.widget.ductareacalculator.roletag.missing = DuctAreaCalculator must have widget component with role tag value "{0}"
error.tagvalidation.reason.widget.ductareacalculator.pointtype.invalid = The source point or slot type of DuctAreaCalculator with Role tag "{0}" should be {1}
error.tagvalidation.reason.widget.ductareacalculator.ducttype.ordinals.invalid = The ordinals of DuctAreaCalculator with ducttype role should be exactly 1 and 2
error.tagvalidation.reason.widget.ductareacalculator.ducttype.enumtext.invalid = The text of enum with key value {0} should contain "{1}" (ignore case)
error.tagvalidation.reason.widget.ductareacalculator.dimensionunit.ordinals.invalid = The ordinals number of DuctAreaCalculator with dimensionunit role should be no more than 4 (Feet,Inch,Centimeter,Meter)
error.tagvalidation.reason.widget.ductareacalculator.dimensionunit.enumtext.invalid = The text of enum of DuctAreaCalculator with dimensionunit role should contain "Feet"/"Inch"/"Centimeter"/"Meter" (case sensitive)
error.tagvalidation.reason.widget.measurementtype.enumtext.invalid = The enum range of MeasurementType should contain at least 2 options, and one with text "Imperial" or "English" and the other with text "Metric" (ignore case)
error.tagvalidation.reason.widget.airflowunitwidget.enumtext.invalid = The enum range of Airflow Unit should contain at least 3 options, and one with text "Cubic Feet Per Minute or CFM", "Cubic Meter Per Hour or CMH" & "Liter Per Second or LPS" (ignore case)
error.tagvalidation.reason.widget.measurementtype.enumtext.invalid = The enum range of MeasurementType should contain at least 2 options, and one with text "Imperial" or "English" and the other with text "Metric" (ignore case)
error.tagvalidation.template.not.exist = Tag Template file does not exist
error.tagvalidation.template.invalid = Tag Template excel file is invalid, please check template excel file in userConfigData folder under Niagara User Home
error.tagvalidation.template.header.index.mismatch = Tag Template file header mismatch, cell index: {0} not found.
error.tagvalidation.template.header.size.mismatch = Tag Template file header mismatch, expected {0} columns, but found {1} columns in tag mapping file.
error.tagvalidation.template.header.value.mismatch = Tag Template file header mismatch, cell value {0} in tag mapping file, but found cell value {1} in tag template file.
error.invalid.set.action = Invalid SET action format: {0}
info.terminal_parse_set_action = Parsed SET Terminal assignment action - Target item: {0}, Value: {1}
info.terminal_set_action = Parsing SET action: {0}
error.rulevalidation.page.notexist = {0} page does not exist
error.rulevalidation.targetitem.notexist = {0} target label not exist in {1}
error.rulevalidation.terminal.notexist = {0} terminal does not exist in device
error.rulevalidation.functionblock.notbeassigned = {0} function block can not be assigned to given terminals {1}.
error.rulevalidation.functionblock.notexist = {0} function block does not exist
error.terminal.set.action.target.item.not.same = function block name must same in a set action


error.rulevalidation.conditional.not.null = Condition cannot be null or empty
error.rulevalidation.action.not.null = Action cannot be null or empty.
error.rulevalidation.query.not.null = Query cannot be null or empty.
error.rulevalidation.visibility.action.not.null = Visibility action cannot be null or empty.

error.rulevalidation.visibility.query.empty.suggestion = Please provide a rule to parsed
error.rulevalidation.visibility.action.null.suggestion = Please provide a valid action.
error.rulevalidation.visibility.condition.null.suggestion = Please provide a valid condition string.
error.rulevalidation.visibility.condition.visib.action.null.suggestion = Please provide a valid visibility action.
error.rulevalidation.visibility.condition.condition.format.suggestion = Please use the correct format: <item> [IN <tab>] <operator> [<value> [IN <tab>]].
error.rulevalidation.condition.operator.suggestion = Please use one of the following operators to form a valid condition: EQUALS, NOT_EQUALS, GREATER_THAN, LESS_THAN, IS_VISIBLE, IS_HIDDEN.
error.rulevalidation.action.format.suggestion = Please use the correct format: SET <item> [IN <tab>] TO <value> [IN <tab>] [ADD|SUBTRACT <value> [IN <tab>]].
error.rulevalidation.terminal.assignment.action.format.suggestion = Please use the correct format: SET <item> TO <value>.

error.tagmappingvalidation.failed = TagMapping validation failed for device "{0}"
error.rulevalidation.visibility.action.format.suggestion = Please use the correct format: SHOW|HIDE <item> [IN <tab>].
error.rulevalidation.visibility.action.type.suggestion = Please use either SHOW or HIDE as the action type.
error.rulevalidation.rule.format.suggestion = Please use the correct format: ADD <RULE_TYPE> RULE TO: <TAB>: IF <CONDITION> THEN <ACTION1>, <ACTION2> ELSE <ACTION3>, <ACTION4>
error.rulevalidation.error.page.suggestion = Please check whether the tab exists in the configuration wizard.

error.rulevalidation.error.target.item.suggestion = Please check the name of the target label is matching with the intended function block name.

error.rulevalidation.error.terminal.suggestion = Please check the terminal configuration in IO folder
error.rulevalidation.rule.type.format.suggestion = Please use correct format: ADD VISIBILITY|VALUE RULE TO

error.rulevalidation.job.log.text = Please double click to see more details.\n

error.rulevalidation.rule.data = Rule Data
error.rulevalidation.rule.format = Rule Format
error.rulevalidation.terminal.duplicate = Duplicate terminal assignment, duplicate terminal is {0}
error.mastersync.target.component.notfound = target component not found, {0}
error.mastersync.target.point.selector.length.error = Target point selector length is not equal to 1
error.mastersync.target.component.property.notfound = Target component property {0} not found in {1}
error.mastersync.validation.failed = master sync validation failed, please check the job log for more details
error.mastersync.honwizardselector.notfound = honeywell wizard selector is not found

error.permission.role.exception = occurred exception during creating wizard related role
error.mastersync.honwizardselector.notfound = honeywell wizard selector is null
error.rulevalidation.constraintrule.format.suggestion = Please use the correct format: ADD <RULE_TYPE> RULE TO: <TAB>: WITH DEADBAND AS: <OBJECT/VALUE> IN <TAB>>>: IF <CONDITION> THEN LIMIT <OBJECT1> IN <TAB> WITH DEADBAND LESS_THAN <OBJECT2> IN <TAB>
error.rulevalidation.constraintrule.operator.error=Invalid rule:{0},Only allow  LESS_THAN and LESS_EQUAL_THAN or GREAT_THAN and GREAT_EQUAL_THAN in a LIMIT
error.rulevalidation.constraintrule.missing.then=Invalid rule:{0}, Missing THEN in rule
error.rulevalidation.constraintrule.invalid.format=Invalid query format:{0}
error.rulevalidation.constraintrule.deadband.prefix.error=Invalid deadband:{0}, must include WITH DEADBAND AS:
error.rulevalidation.constraintrule.invalid.limit.format=Invalid LIMIT format:{0}
error.rulevalidation.constraintrule.unit.error=All units must be same in a LIMIT rule occurs in {0}.
error.rulevalidation.constraintrule.operator.suggestion=Please use same operators in a LIMIT.
error.rulevalidation.constraintrule.unit.suggestion=Please make sure all points are some units in a constraint rule.
error.rulevalidation.constraintrule.unit.deadband.error=All points unit and deadband unit should be same in a LIMIT rule occurs in {0}.
error.rulevalidation.constraintrule.unit.deadband.suggestion=Please make sure all points and deadband unit are same in a constraint rule.
error.rulevalidation.constraintrule.point.unit.error=Point and deadband should include unit slot in wizard configuration.
error.rulevalidation.constraintrule.point.unit.suggestion=Please make sure all points and deadband have unit slot in wizard configuration.
error.rulevalidation.label.duplicate.error=Duplicate label found: {0} in page: {1} and it is a range slider widget. This may cause unexpected behavior.
error.rulevalidation.targetitem.role.notexist = {0} role does not exist in {1} of {2}
error.rulevalidation.error.target.role.suggestion=Please check the role of the target item is matching with role of the function block.

error.copy.excel.template.file = Failed to copy tag template mapping file to station home.
error.creating.userconfigdata.folder = Failed to create userConfigData folder in station home.

#Timezone
Etc_GMT=International Date Line West (GMT-12:00)
Pacific_Midway=Midway Island, Samoa (GMT-11:00)
Pacific_Honolulu=Hawaii (GMT-10:00)
US_Alaska=Alaska (GMT-09:00)
America_Los_Angeles=Pacific Time (US & Canada) (GMT-08:00)
America_Tijuana=Tijuana, Baja California (GMT-08:00)
US_Arizona=Arizona (GMT-07:00)
America_Chihuahua=Chihuahua, La Paz, Mazatlan (GMT-07:00)
US_Mountain=Mountain Time (US & Canada) (GMT-07:00)
America_Managua=Central America (GMT-06:00)
US_Central=Central Time (US & Canada) (GMT-06:00)
America_Mexico_City=Guadalajara, Mexico City, Monterrey (GMT-06:00)
Canada_Saskatchewan=Saskatchewan (GMT-06:00)
America_Bogota=Bogota, Lima, Quito, Rio Branco (GMT-05:00)
US_Eastern=Eastern Time (US & Canada) (GMT-05:00)
US_East_Indiana=Indiana (East) (GMT-05:00)
Canada_Atlantic=Atlantic Time (Canada) (GMT-04:00)
America_Caracas=Caracas, La Paz (GMT-04:00)
America_Manaus=Manaus (GMT-04:00)
America_Santiago=Santiago (GMT-04:00)
Canada_Newfoundland=Newfoundland (GMT-03:30)
America_Sao_Paulo=Brasilia (GMT-03:00)
America_Argentina_Buenos_Aires=Buenos Aires, Georgetown (GMT-03:00)
America_Godthab=Greenland (GMT-03:00)
America_Montevideo=Montevideo (GMT-03:00)
America_Noronha=Mid-Atlantic (GMT-02:00)
Atlantic_Cape_Verde=Cape Verde Is. (GMT-01:00)
Atlantic_Azores=Azores (GMT-01:00)
Africa_Casablanca=Casablanca, Monrovia, Reykjavik (GMT+00:00)
Etc_Greenwich=Greenwich Mean Time : Dublin, Edinburgh, Lisbon, London (GMT+00:00)
Europe_Amsterdam=Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna (GMT+01:00)
Europe_Belgrade=Belgrade, Bratislava, Budapest, Ljubljana, Prague (GMT+01:00)
Europe_Brussels=Brussels, Copenhagen, Madrid, Paris (GMT+01:00)
Europe_Sarajevo=Sarajevo, Skopje, Warsaw, Zagreb (GMT+01:00)
Africa_Lagos=West Central Africa (GMT+01:00)
Asia_Amman=Amman (GMT+02:00)
Europe_Athens=Athens, Bucharest, Istanbul (GMT+02:00)
Asia_Beirut=Beirut (GMT+02:00)
Africa_Cairo=Cairo (GMT+02:00)
Africa_Harare=Harare, Pretoria (GMT+02:00)
Europe_Helsinki=Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius (GMT+02:00)
Asia_Jerusalem=Jerusalem (GMT+02:00)
Europe_Minsk=Minsk (GMT+02:00)
Africa_Windhoek=Windhoek (GMT+02:00)
Asia_Kuwait=Kuwait, Riyadh, Baghdad (GMT+03:00)
Europe_Moscow=Moscow, St. Petersburg, Volgograd (GMT+03:00)
Africa_Nairobi=Nairobi (GMT+03:00)
Asia_Tbilisi=Tbilisi (GMT+03:00)
Asia_Tehran=Tehran (GMT+03:30)
Asia_Muscat=Abu Dhabi, Muscat (GMT+04:00)
Asia_Baku=Baku (GMT+04:00)
Asia_Yerevan=Yerevan (GMT+04:00)
Asia_Kabul=(GMT+04:30) Kabul
Asia_Yekaterinburg=Yekaterinburg (GMT+05:00)
Asia_Karachi=Islamabad, Karachi, Tashkent (GMT+05:00)
Asia_Calcutta=Chennai, Kolkata, Mumbai, New Delhi (GMT+05:30)
Asia_Katmandu=Kathmandu (GMT+05:45)
Asia_Almaty=Almaty, Novosibirsk (GMT+06:00)
Asia_Dhaka=Astana, Dhaka (GMT+06:00)
Asia_Rangoon=Yangon (Rangoon) (GMT+06:30)
Asia_Bangkok=Bangkok, Hanoi, Jakarta (GMT+07:00)
Asia_Krasnoyarsk=Krasnoyarsk (GMT+07:00)
Asia_Hong_Kong=Beijing, Chongqing, Hong Kong, Urumqi (GMT+08:00)
Asia_Kuala_Lumpur=Kuala Lumpur, Singapore (GMT+08:00)
Asia_Irkutsk=Irkutsk, Ulaan Bataar (GMT+08:00)
Australia_Perth=Perth (GMT+08:00)
Asia_Taipei=Taipei (GMT+08:00)
Asia_Tokyo=Osaka, Sapporo, Tokyo (GMT+09:00)
Asia_Seoul=Seoul (GMT+09:00)
Asia_Yakutsk=Yakutsk (GMT+09:00)
Australia_Adelaide=Adelaide (GMT+09:30)
Australia_Darwin=Darwin (GMT+09:30)
Australia_Brisbane=Brisbane (GMT+10:00)
Australia_Canberra=Canberra, Melbourne, Sydney (GMT+10:00)
Australia_Hobart=Hobart (GMT+10:00)
Pacific_Guam=Guam, Port Moresby (GMT+10:00)
Asia_Vladivostok=Vladivostok (GMT+10:00)
Asia_Magadan=Magadan, Solomon Is., New Caledonia (GMT+11:00)
Pacific_Auckland=Auckland, Wellington (GMT+12:00)
Pacific_Fiji=Fiji, Kamchatka, Marshall Is. (GMT+12:00)
Pacific_Tongatapu=Nuku'alofa (GMT+13:00)

#Wizard EncodeDecode
honWizardJob.encode.start = Encoding started.
honWizardJob.encode.completed = Encoding completed successfully.
honWizardJob.encode.failed = Wizard Data Encoding failed because of {0} : {1}
honWizardJob.encode.size = Encoded data size: {0} bytes.
honWizardJob.encode.repeatedTagMappings.start = Encoding repeated tag mappings started.
honWizardJob.encode.repeatedTagMappings.completed = Encoding repeated tag mappings completed.
honWizardJob.encode.repeatedTagMappings = Encoded repeated tag mapping: ID={0}, Value={1}.
honWizardJob.encode.metaData.start = Encoding metadata started.
honWizardJob.encode.metaData.completed = Encoding metadata completed.
honWizardJob.encode.metaData = Encoded metadata: Key={0}, Value={1}.
honWizardJob.encode.rules.start = Encoding rules started.
honWizardJob.encode.rules.completed = Encoding rules completed successfully.
honWizardJob.encode.rules.rule = Encoded rule: Name={0}, Compressed Size={1} bytes.
honWizardJob.encode.pageOrder.start = Encoding page order started.
honWizardJob.encode.pageOrder.completed = Encoding page order completed successfully.
honWizardJob.encode.pageOrder.repeatedValue = Encoded repeated page order value: PageName={0}, RepeatedValueID={1}.
honWizardJob.encode.pageOrder.fullValue = Encoded full page order value: PageName={0}, TagID={1}.
honWizardJob.encode.tagMappings.start = Encoding tag mappings started.
honWizardJob.encode.tagMappings.completed = Encoding tag mappings completed successfully.
honWizardJob.encode.tagMappings.repeatedValue = Encoded repeated tag mapping: TagName={0}, RepeatedValueID={1}.
honWizardJob.encode.tagMappings.fullValue = Encoded full tag mapping: TagName={0}, Value={1}.
honWizardJob.encode.slotTagMappings.start = Encoding slot tag mappings started.
honWizardJob.encode.slotTagMappings.completed = Encoding slot tag mappings completed successfully.
honWizardJob.encode.slotTagMappings.slotSelector = Encoded slot selector: CommonSlotPath={0}, SourcePropertyName={1}, TagsCount={2}.
honWizardJob.encode.value.start = Encoding value of type {0} started.
honWizardJob.encode.value.completed = Encoding value of type {0} completed.
honWizardJob.encode.value.unsupported = Unsupported value type: {0}.
honWizardJob.decode.start = Decoding started.
honWizardJob.decode.completed = Wizard Data Decoding completed successfully.
honWizardJob.decode.failed = Wizard Data Decoding failed because of {0} : {1}
honWizardJob.decode.version = Decoded version: {0}.
honWizardJob.decode.repeatedTagMappings.start = Decoding repeated tag mappings started.
honWizardJob.decode.repeatedTagMappings.completed = Decoding repeated tag mappings completed.
honWizardJob.decode.tagMappings.repeatedValue = Decoded repeated tag mapping: TagID={0}, RepeatedValueID={1}, Value={2}.
honWizardJob.decode.tagMappings.fullValue = Decoded full tag mapping: TagID={0}, Value={1}.
honWizardJob.decode.tagMappings.selector = Decoded selector with tags: {0}.
honWizardJob.decode.tagMappings.completed=Decode Tag Mappings completed successfully.
honWizardJob.decode.slotTagMappings.start = Decoding slot tag mappings started.
honWizardJob.decode.slotTagMappings.completed = Decoding slot tag mappings completed successfully.
honWizardJob.decode.slotTagMappings.slotSelector = Decoded slot selector: ParentSlotPath={0}, SourcePropertyName={1}, TagsCount={2}.
honWizardJob.decode.metaData.start = Decoding metadata started.
honWizardJob.decode.metaData.completed = Decoding metadata completed.
honWizardJob.decode.metaData = Decoded metadata: Key={0}, Value={1}.
honWizardJob.decode.rules.start = Decoding rules started.
honWizardJob.decode.rules.completed = Decoding rules completed successfully.
honWizardJob.decode.rules.rule = Decoded rule: Name={0}, Content={1}.
honWizardJob.decode.pageOrder.start = Decoding page order started.
honWizardJob.decode.pageOrder.completed = Decoding page order completed successfully.
honWizardJob.decode.pageOrder.repeatedValue = Decoded repeated page order value: Index={0}, RepeatedValueID={1}, PageName={2}.
honWizardJob.decode.pageOrder.fullValue = Decoded full page order value: Index={0}, PageName={1}.
honWizardJob.decode.value.start = Decoding value with type indicator {0} started.
honWizardJob.decode.value.completed = Decoded value with type indicator {0}: {1}.
honWizardJob.decode.value.unsupported = Unsupported type indicator: {0}.
honWizardJob.encode.slotTagMappings.error = Error occurred while encoding slot tag mappings: {0}.
honWizardJob.decode.slotTagMappings.error = Error occurred while decoding slot tag mappings: {0}.
honWizardJob.decode.slotTagMappings.selectorMismatch = Mismatch between decoded selectors and slot selectors: Expected={0}, Found={1}.
honWizardJob.decode.slotTagMappings.invalidStructure = Invalid structure detected while decoding slot tag mappings.
honWizardJob.collectRepeatedValues.start = Collecting repeated tag values started.
honWizardJob.collectRepeatedValues.completed = Collecting repeated tag values completed.
honWizardJob.collectRepeatedValues.error = Error occurred while collecting repeated values: {0}.
honWizardJob.decode.value.error = Error occurred while decoding value with type indicator {0}: {1}.
honWizardJob.encode.value.error = Error occurred while encoding value of type {0}: {1}.
honWizardJob.file.save.start = Saving encoded data to file: {0}.
honWizardJob.file.save.completed = File saved successfully: {0}.
honWizardJob.file.save = Saved file: {0}, Size: {1} bytes.
honWizardJob.file.save.error = Error saving file: {0}.
honWizardJob.collectRepeatedValues.start = Collecting repeated tag values started.
honWizardJob.collectRepeatedValues.completed = Collecting repeated tag values completed.
honWizardJob.tagMapping.add = Added repeated value mapping: Value={0}, ID={1}.
honWizardJob.byteReader.readByte = Read byte: {0}.
honWizardJob.byteReader.readString = Read string: {0}.
honWizardJob.byteReader.readInt = Read integer: {0}.
honWizardJob.byteReader.readDouble = Read double: {0}.
honWizardJob.byteReader.readBoolean = Read boolean: {0}.
honWizardJob.byteBuilder.addByte = Added byte: {0}.
honWizardJob.byteBuilder.addString = Added string: {0}.
honWizardJob.byteBuilder.addInt = Added integer: {0}.
honWizardJob.byteBuilder.addDouble = Added double: {0}.
honWizardJob.byteBuilder.addBoolean = Added boolean: {0}.
honWizardJob.byteBuilder.addBytes = Added bytes: Start={0}, Length={1}.

error.tagvalidation.widgettype.duplicated = Widget Type duplicated
error.tagvalidation.duplicated.widgettype = Duplicated widget type: {0}
error.tagvalidation.duplicated.widgettype.selectors = Selectors with duplicated widget type:
error.tagvalidation.reason.widgettype.duplicated = Widget is only allowed to be used once in a page.

error.palette.point.selector.no.support = The point does not support being added as {0} in the wizard. Please add any of the following UI widget tags\n{1}
error.palette.point.selector.exist = UI Widget Tag has already been associated with the point. Please delete the existing UI Widget Tag and add.
error.palette.slot.selector.no.support = None of the slots in current component can support being attached to {0} UI widget tag. Please add any of the following widget tags\n{1}
error.palette.slot.selector.schedule.no.support = Schedule UI widget tag can only be associated with schedule point
error.palette.slot.selector.no.support.component = None of the slots in current component can support being attached to The UI widget tag
error.palette.slot.selector.no.support.expertmode.disabled = UI Widget Tags can't be added when expert mode is disabled.
error.delete.tag.not.allowed.expertmode.disable = Tags can't be removed when expert mode is disabled
error.add.tag.not.allowed.expertmode.disable = New Tags can't be added when expert mode is disabled
error.edit.tag.not.allowed.expertmode.disable = Tag values can't be updated when expert mode is disabled