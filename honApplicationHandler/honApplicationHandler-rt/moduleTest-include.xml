<!-- Module Test Include File -->
<!-- Types -->
<types>
  <!-- Keep only correct implementations -->
  <type name="ApplicationHandlerSimpleTest" class="com.honeywell.application.handler.test.BApplicationHandlerSimpleTest"/>
  <type name="ApplicationHandlerWidgetTest" class="com.honeywell.application.handler.test.BApplicationHandlerWidgetTest"/>
  <type name="SwitchButtonFixTest" class="com.honeywell.application.handler.test.BSwitchButtonFixTest"/>
  <type name="HonWizardGlobalStoreTestSimple" class="com.honeywell.application.handler.test.BHonWizardGlobalStoreTestSimple"/>
  <type name="MonthEnumTest" class="com.honeywell.application.handler.test.enums.BMonthEnumTest"/>
</types>